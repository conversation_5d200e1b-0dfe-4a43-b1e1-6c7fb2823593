--[[
	MonsterConfig - 怪物配置
	定義所有怪物的屬性、外觀和行為
]]

local MonsterConfig = {}

-- 怪物配置數據
local monsters = {
	slime = {
		name = "綠色史萊姆",
		level = 1,
		stats = {
			maxHealth = 50,
			attack = 8,
			defense = 2,
			speed = 12
		},
		appearance = {
			color = Color3.fromRGB(100, 255, 100),
			size = Vector3.new(3, 3, 3),
			shape = "Ball",
			material = Enum.Material.Neon
		},
		behavior = {
			attackRange = 6,
			chaseRange = 20,
			attackCooldown = 2,
			wanderRadius = 10
		},
		rewards = {
			experience = 15,
			coins = 8
		},
		sounds = {
			spawn = "rbxasset://sounds/electronicpingshort.wav",
			attack = "rbxasset://sounds/impact_generic.mp3",
			death = "rbxasset://sounds/uuhhh.mp3"
		}
	},
	
	goblin = {
		name = "哥布林戰士",
		level = 3,
		stats = {
			maxHealth = 80,
			attack = 15,
			defense = 5,
			speed = 16
		},
		appearance = {
			color = Color3.fromRGB(139, 69, 19),
			size = Vector3.new(2, 4, 2),
			shape = "Block",
			material = Enum.Material.Plastic
		},
		behavior = {
			attackRange = 8,
			chaseRange = 25,
			attackCooldown = 1.5,
			wanderRadius = 15
		},
		rewards = {
			experience = 25,
			coins = 15
		},
		sounds = {
			spawn = "rbxasset://sounds/button.wav",
			attack = "rbxasset://sounds/sword_slash.mp3",
			death = "rbxasset://sounds/uuhhh.mp3"
		}
	},
	
	fire_elemental = {
		name = "火焰元素",
		level = 5,
		stats = {
			maxHealth = 120,
			attack = 25,
			defense = 8,
			speed = 20
		},
		appearance = {
			color = Color3.fromRGB(255, 100, 0),
			size = Vector3.new(3, 5, 3),
			shape = "Ball",
			material = Enum.Material.Neon
		},
		behavior = {
			attackRange = 12,
			chaseRange = 30,
			attackCooldown = 1.8,
			wanderRadius = 20,
			canFly = true
		},
		rewards = {
			experience = 40,
			coins = 25
		},
		sounds = {
			spawn = "rbxasset://sounds/electronicpingshort.wav",
			attack = "rbxasset://sounds/impact_generic.mp3",
			death = "rbxasset://sounds/uuhhh.mp3"
		}
	}
}

-- 獲取怪物配置
function MonsterConfig.getMonster(monsterId)
	return monsters[monsterId]
end

-- 獲取所有怪物
function MonsterConfig.getAllMonsters()
	return monsters
end

-- 獲取隨機怪物ID
function MonsterConfig.getRandomMonsterId()
	local monsterIds = {}
	for id, _ in pairs(monsters) do
		table.insert(monsterIds, id)
	end
	return monsterIds[math.random(1, #monsterIds)]
end

-- 根據等級獲取合適的怪物
function MonsterConfig.getMonsterByLevel(playerLevel)
	local suitableMonsters = {}
	for id, monster in pairs(monsters) do
		if monster.level <= playerLevel + 2 and monster.level >= playerLevel - 1 then
			table.insert(suitableMonsters, id)
		end
	end
	
	if #suitableMonsters > 0 then
		return suitableMonsters[math.random(1, #suitableMonsters)]
	else
		return "slime" -- 默認返回史萊姆
	end
end

return MonsterConfig
