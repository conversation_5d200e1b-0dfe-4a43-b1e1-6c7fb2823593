--[[
	en-US.lua - English language pack
]]

return {
	-- Common
	common = {
		ok = "OK",
		cancel = "Cancel",
		close = "Close",
		back = "Back",
		next = "Next",
		previous = "Previous",
		confirm = "Confirm",
		loading = "Loading...",
		error = "Error",
		success = "Success",
		warning = "Warning",
		info = "Information",
	},
	
	-- UI Interface
	ui = {
		buttons = {
			attack = "Attack",
			summon = "Summon",
			recall = "Recall",
			gacha = "Gacha",
			inventory = "Inventory",
			settings = "Settings",
			quit = "Quit",
		},
		
		menus = {
			main = "Main Menu",
			pet = "Pets",
			weapon = "Weapons",
			gacha = "Gacha",
			profile = "Profile",
			achievements = "Achievements",
			settings = "Settings",
		},
		
		labels = {
			level = "Level",
			experience = "Experience",
			health = "Health",
			attack = "Attack",
			defense = "Defense",
			speed = "Speed",
			coins = "Coins",
			gems = "Gems",
			rarity = "Rarity",
		},
	},
	
	-- Pet System
	pets = {
		title = "Pet System",
		summon_success = "Successfully summoned {petName}!",
		summon_failed = "Summon failed",
		recall_success = "Pet recalled",
		not_owned = "You don't own this pet",
		already_summoned = "Pet already summoned",
		level_up = "{petName} leveled up!",
		
		rarities = {
			Common = "Common",
			Uncommon = "Uncommon",
			Rare = "Rare",
			Epic = "Epic",
			Legendary = "Legendary",
		},
		
		abilities = {
			Bounce = "Bounce Attack",
			Howl = "Howl Buff",
			Fireball = "Fireball",
			Burn = "Burn Effect",
			IceBreath = "Ice Breath",
			Freeze = "Freeze Effect",
			DragonBreath = "Dragon Breath",
			Flight = "Flight",
			Roar = "Intimidating Roar",
		},
	},
	
	-- Combat System
	combat = {
		attack_hit = "Hit! Dealt {damage} damage",
		critical_hit = "Critical! Dealt {damage} damage",
		enemy_defeated = "Enemy defeated!",
		player_died = "You died!",
		pet_died = "Pet died",
		out_of_range = "Out of range",
		cooldown = "Skill on cooldown",
	},
	
	-- Gacha System
	gacha = {
		title = "Gacha System",
		single_pull = "Single Pull",
		ten_pull = "10x Pull",
		cost = "Cost: {cost} coins",
		insufficient_coins = "Insufficient coins",
		congratulations = "Congratulations",
		new_pet = "New pet!",
		duplicate = "Duplicate pet (converted to experience)",
		
		results = {
			common = "Got common pet",
			uncommon = "Got uncommon pet",
			rare = "Got rare pet!",
			epic = "Got epic pet!!",
			legendary = "Got legendary pet!!!",
		},
	},
	
	-- Settings
	settings = {
		title = "Game Settings",
		language = "Language",
		music_volume = "Music Volume",
		sfx_volume = "SFX Volume",
		auto_attack = "Auto Attack",
		show_damage = "Show Damage Numbers",
		graphics_quality = "Graphics Quality",
		
		quality_levels = {
			low = "Low",
			medium = "Medium",
			high = "High",
			ultra = "Ultra",
		},
	},
	
	-- Achievements
	achievements = {
		title = "Achievement System",
		unlocked = "Achievement unlocked!",
		progress = "Progress: {current}/{total}",
		
		names = {
			first_pet = "First Pet",
			pet_collector = "Pet Collector",
			monster_slayer = "Monster Slayer",
			level_up = "Level Master",
			gacha_master = "Gacha Master",
		},
		
		descriptions = {
			first_pet = "Obtain your first pet",
			pet_collector = "Collect 10 different pets",
			monster_slayer = "Defeat 100 monsters",
			level_up = "Reach level 10",
			gacha_master = "Perform 100 gacha pulls",
		},
	},
	
	-- Error Messages
	errors = {
		connection_lost = "Connection lost",
		data_load_failed = "Failed to load data",
		save_failed = "Save failed",
		invalid_action = "Invalid action",
		server_error = "Server error",
		timeout = "Operation timeout",
	},
	
	-- Notifications
	notifications = {
		welcome = "Welcome to the game!",
		daily_reward = "Daily reward received!",
		level_up = "Congratulations! Reached level {level}!",
		new_achievement = "New achievement: {achievement}",
		maintenance = "Server under maintenance",
	},
}
