--[[
	ECS 載入器
	負責載入和管理所有 Matter ECS 系統
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local RunService = game:GetService("RunService")

local ECSLoader = {}

-- 系統列表
local systems = {}
local systemConnections = {}

-- 載入所有系統
function ECSLoader.loadSystems(world)
	local SystemsFolder = script.Parent.Systems
	
	-- 載入所有系統模組
	for _, systemModule in pairs(SystemsFolder:GetChildren()) do
		if systemModule:IsA("ModuleScript") then
			local systemFunction = require(systemModule)
			if typeof(systemFunction) == "function" then
				table.insert(systems, {
					name = systemModule.Name,
					system = systemFunction,
					enabled = true
				})
				print("📦 Loaded ECS System:", systemModule.Name)
			end
		end
	end
	
	print("⚙️ Total ECS Systems loaded:", #systems)
end

-- 啟動所有系統
function ECSLoader.startSystems(world)
	for _, systemData in pairs(systems) do
		if systemData.enabled then
			-- 創建系統連接
			local connection = RunService.Heartbeat:Connect(function(deltaTime)
				systemData.system(world, deltaTime)
			end)
			
			systemConnections[systemData.name] = connection
			print("▶️ Started ECS System:", systemData.name)
		end
	end
end

-- 停止所有系統
function ECSLoader.stopSystems()
	for systemName, connection in pairs(systemConnections) do
		connection:Disconnect()
		systemConnections[systemName] = nil
		print("⏹️ Stopped ECS System:", systemName)
	end
end

-- 啟用/禁用特定系統
function ECSLoader.setSystemEnabled(systemName, enabled)
	for _, systemData in pairs(systems) do
		if systemData.name == systemName then
			systemData.enabled = enabled
			
			if enabled and not systemConnections[systemName] then
				-- 啟動系統
				local connection = RunService.Heartbeat:Connect(function(deltaTime)
					systemData.system(_G.MatterWorld, deltaTime)
				end)
				systemConnections[systemName] = connection
				print("▶️ Enabled ECS System:", systemName)
			elseif not enabled and systemConnections[systemName] then
				-- 停止系統
				systemConnections[systemName]:Disconnect()
				systemConnections[systemName] = nil
				print("⏸️ Disabled ECS System:", systemName)
			end
			break
		end
	end
end

-- 獲取系統狀態
function ECSLoader.getSystemStatus()
	local status = {}
	for _, systemData in pairs(systems) do
		status[systemData.name] = {
			enabled = systemData.enabled,
			running = systemConnections[systemData.name] ~= nil
		}
	end
	return status
end

return ECSLoader
