--[[
	PerformanceTest - 性能測試工具
	用於測試戰鬥系統優化效果
]]

local PerformanceTest = {}

local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- 測試統計
local testStats = {
	frameCount = 0,
	totalFrameTime = 0,
	maxFrameTime = 0,
	minFrameTime = math.huge,
	lastFrameTime = 0,
	startTime = 0,
	monsterCount = 0,
	playerCount = 0
}

-- 性能監控連接
local performanceConnection = nil

-- 開始性能測試
function PerformanceTest.startTest()
	testStats.startTime = tick()
	testStats.frameCount = 0
	testStats.totalFrameTime = 0
	testStats.maxFrameTime = 0
	testStats.minFrameTime = math.huge
	testStats.lastFrameTime = tick()
	
	performanceConnection = RunService.Heartbeat:Connect(function()
		PerformanceTest._updateStats()
	end)
	
	print("📊 Performance test started")
end

-- 停止性能測試
function PerformanceTest.stopTest()
	if performanceConnection then
		performanceConnection:Disconnect()
		performanceConnection = nil
	end
	
	local results = PerformanceTest.getResults()
	print("📊 Performance test stopped")
	print("📊 Results:", results)
	return results
end

-- 更新統計數據
function PerformanceTest._updateStats()
	local currentTime = tick()
	local frameTime = currentTime - testStats.lastFrameTime
	
	testStats.frameCount = testStats.frameCount + 1
	testStats.totalFrameTime = testStats.totalFrameTime + frameTime
	testStats.maxFrameTime = math.max(testStats.maxFrameTime, frameTime)
	testStats.minFrameTime = math.min(testStats.minFrameTime, frameTime)
	testStats.lastFrameTime = currentTime
	
	-- 更新怪物和玩家數量
	testStats.monsterCount = PerformanceTest._countMonsters()
	testStats.playerCount = #Players:GetPlayers()
end

-- 計算怪物數量
function PerformanceTest._countMonsters()
	local count = 0
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") then
			count = count + 1
		end
	end
	return count
end

-- 獲取測試結果
function PerformanceTest.getResults()
	if testStats.frameCount == 0 then
		return {
			error = "No data collected"
		}
	end
	
	local avgFrameTime = testStats.totalFrameTime / testStats.frameCount
	local avgFPS = 1 / avgFrameTime
	local testDuration = tick() - testStats.startTime
	
	return {
		duration = testDuration,
		frameCount = testStats.frameCount,
		averageFPS = avgFPS,
		maxFPS = 1 / testStats.minFrameTime,
		minFPS = 1 / testStats.maxFrameTime,
		averageFrameTime = avgFrameTime * 1000, -- 轉換為毫秒
		maxFrameTime = testStats.maxFrameTime * 1000,
		minFrameTime = testStats.minFrameTime * 1000,
		monsterCount = testStats.monsterCount,
		playerCount = testStats.playerCount
	}
end

-- 獲取實時統計
function PerformanceTest.getLiveStats()
	if testStats.frameCount == 0 then
		return nil
	end
	
	local avgFrameTime = testStats.totalFrameTime / testStats.frameCount
	local currentFPS = 1 / (tick() - testStats.lastFrameTime)
	
	return {
		currentFPS = currentFPS,
		averageFPS = 1 / avgFrameTime,
		frameCount = testStats.frameCount,
		monsterCount = testStats.monsterCount,
		playerCount = testStats.playerCount,
		testDuration = tick() - testStats.startTime
	}
end

-- 壓力測試：生成大量怪物
function PerformanceTest.stressTestMonsters(count)
	local MonsterService = nil
	
	-- 嘗試獲取 MonsterService（服務端）
	local success, result = pcall(function()
		local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
		return Knit.GetService("MonsterService")
	end)
	
	if success and result then
		MonsterService = result
	else
		warn("PerformanceTest: Cannot access MonsterService (client-side?)")
		return false
	end
	
	print("📊 Starting stress test with", count, "monsters")
	
	-- 在隨機位置生成怪物
	for i = 1, count do
		local position = Vector3.new(
			math.random(-50, 50),
			10,
			math.random(-50, 50)
		)
		MonsterService:SpawnMonster(nil, nil, position)
		
		-- 每10個怪物暫停一下，避免卡頓
		if i % 10 == 0 then
			task.wait(0.1)
		end
	end
	
	print("📊 Stress test setup complete")
	return true
end

-- 清理所有測試怪物
function PerformanceTest.cleanupTestMonsters()
	local count = 0
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") then
			model:Destroy()
			count = count + 1
		end
	end
	print("📊 Cleaned up", count, "test monsters")
	return count
end

-- 運行完整的性能測試套件
function PerformanceTest.runFullTest(duration, monsterCount)
	duration = duration or 30 -- 默認30秒
	monsterCount = monsterCount or 20 -- 默認20個怪物
	
	print("📊 Starting full performance test")
	print("📊 Duration:", duration, "seconds")
	print("📊 Monster count:", monsterCount)
	
	-- 清理現有怪物
	PerformanceTest.cleanupTestMonsters()
	
	-- 生成測試怪物
	if not PerformanceTest.stressTestMonsters(monsterCount) then
		warn("📊 Failed to setup stress test")
		return nil
	end
	
	-- 等待怪物完全生成
	task.wait(2)
	
	-- 開始性能測試
	PerformanceTest.startTest()
	
	-- 等待測試完成
	task.wait(duration)
	
	-- 停止測試並獲取結果
	local results = PerformanceTest.stopTest()
	
	-- 清理測試怪物
	PerformanceTest.cleanupTestMonsters()
	
	print("📊 Full performance test completed")
	return results
end

-- 輸出格式化的測試報告
function PerformanceTest.printReport(results)
	if not results or results.error then
		print("📊 No valid test results")
		return
	end
	
	print("=" .. string.rep("=", 50))
	print("📊 PERFORMANCE TEST REPORT")
	print("=" .. string.rep("=", 50))
	print("⏱️  Test Duration:", string.format("%.2f", results.duration), "seconds")
	print("🎯 Frame Count:", results.frameCount)
	print("📈 Average FPS:", string.format("%.1f", results.averageFPS))
	print("📊 Max FPS:", string.format("%.1f", results.maxFPS))
	print("📉 Min FPS:", string.format("%.1f", results.minFPS))
	print("⚡ Avg Frame Time:", string.format("%.2f", results.averageFrameTime), "ms")
	print("🐌 Max Frame Time:", string.format("%.2f", results.maxFrameTime), "ms")
	print("⚡ Min Frame Time:", string.format("%.2f", results.minFrameTime), "ms")
	print("👹 Monster Count:", results.monsterCount)
	print("👥 Player Count:", results.playerCount)
	print("=" .. string.rep("=", 50))
	
	-- 性能評級
	local grade = "Unknown"
	if results.averageFPS >= 55 then
		grade = "Excellent (A+)"
	elseif results.averageFPS >= 45 then
		grade = "Good (A)"
	elseif results.averageFPS >= 35 then
		grade = "Fair (B)"
	elseif results.averageFPS >= 25 then
		grade = "Poor (C)"
	else
		grade = "Very Poor (D)"
	end
	
	print("🏆 Performance Grade:", grade)
	print("=" .. string.rep("=", 50))
end

return PerformanceTest
