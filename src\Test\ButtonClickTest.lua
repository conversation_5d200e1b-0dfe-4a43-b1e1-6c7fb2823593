--[[
	ButtonClickTest.lua - 測試按鈕點擊功能
	檢查 UI 按鈕是否正確觸發召喚
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🔘 Button Click Test Starting...")

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待 UI 初始化
	task.wait(3)
	
	-- 獲取控制器
	local PetController = Knit.GetController("PetController")
	local UIController = Knit.GetController("UIController")
	
	if not PetController then
		warn("❌ PetController not found!")
		return
	end
	
	print("✅ PetController found")
	
	-- 檢查 PetController 的方法
	print("\n📋 PetController methods:")
	for key, value in pairs(PetController) do
		if type(value) == "function" then
			print("  -", key)
		end
	end
	
	-- 添加測試寵物
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	
	if profile and not profile.Data.ownedPets.Slime then
		print("💰 Adding Slime pet for testing...")
		PlayerProfile.addPet(player, "Slime", {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
	end
	
	-- 監聽召喚信號
	local PetService = Knit.GetService("PetService")
	local signalFired = false
	
	-- 攔截 SummonPet 信號
	local originalFire = PetService.SummonPet.Fire
	PetService.SummonPet.Fire = function(self, petId)
		signalFired = true
		print("✅ SummonPet signal intercepted! Pet:", petId)
		return originalFire(self, petId)
	end
	
	-- 測試直接調用召喚函數
	print("\n🧪 Test 1: Direct function call...")
	if PetController.SummonSelectedPet then
		local success, err = pcall(function()
			PetController:SummonSelectedPet()
		end)
		
		if success then
			print("✅ Direct function call succeeded")
		else
			print("❌ Direct function call failed:", err)
		end
	end
	
	task.wait(2)
	
	-- 測試手動設置選中寵物
	print("\n🧪 Test 2: Manual pet selection...")
	
	-- 嘗試訪問 PetController 的狀態
	local success, err = pcall(function()
		-- 這裡我們需要手動設置選中的寵物
		-- 由於我們無法直接訪問 Fusion 狀態，我們嘗試其他方法
		
		-- 檢查是否有公開的方法來設置選中寵物
		if PetController.SelectPet then
			PetController:SelectPet("Slime")
			print("✅ Pet selected via SelectPet method")
		else
			print("⚠️ No SelectPet method found")
		end
	end)
	
	if not success then
		print("❌ Manual selection failed:", err)
	end
	
	-- 再次嘗試召喚
	if PetController.SummonSelectedPet then
		local success, err = pcall(function()
			PetController:SummonSelectedPet()
		end)
		
		if success then
			print("✅ Second function call succeeded")
		else
			print("❌ Second function call failed:", err)
		end
	end
	
	task.wait(2)
	
	-- 測試直接發送信號
	print("\n🧪 Test 3: Direct signal...")
	print("Manually firing SummonPet signal...")
	PetService.SummonPet:Fire("Slime")
	
	task.wait(3)
	
	-- 檢查 UI 元素
	print("\n🧪 Test 4: UI Element check...")
	local playerGui = player:WaitForChild("PlayerGui")
	
	-- 查找寵物 UI
	local petUI = playerGui:FindFirstChild("PetUI")
	if petUI then
		print("✅ PetUI found")
		
		-- 查找召喚按鈕
		local function findButton(parent, depth)
			if depth > 5 then return nil end
			
			for _, child in ipairs(parent:GetChildren()) do
				if child:IsA("TextButton") and (child.Name:match("Summon") or child.Text:match("召喚")) then
					return child
				end
				
				local found = findButton(child, depth + 1)
				if found then return found end
			end
			return nil
		end
		
		local summonButton = findButton(petUI, 0)
		if summonButton then
			print("✅ Summon button found:", summonButton.Name)
			print("  Text:", summonButton.Text)
			print("  Enabled:", summonButton.Interactable)
			
			-- 嘗試點擊按鈕
			print("🔘 Simulating button click...")
			summonButton.Activated:Fire()
		else
			print("❌ Summon button not found")
		end
	else
		print("❌ PetUI not found")
	end
	
	task.wait(2)
	
	-- 結果總結
	print("\n" .. string.rep("=", 50))
	print("🧪 BUTTON TEST RESULTS")
	print(string.rep("=", 50))
	
	print("Signal Fired:", signalFired and "✅ YES" or "❌ NO")
	
	if not signalFired then
		print("\n🔧 TROUBLESHOOTING:")
		print("1. Check if pet is selected in UI")
		print("2. Check if pet data is loaded")
		print("3. Check button click handler")
		print("4. Check Fusion state management")
		print("5. Try using /summon command instead")
	else
		print("\n✅ Signal system works!")
		print("If pet doesn't appear, check server-side summoning logic")
	end
	
	print(string.rep("=", 50))
	
end):catch(function(err)
	warn("❌ Button test failed:", err)
end)

return true
