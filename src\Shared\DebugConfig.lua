--[[
	DebugConfig - 調試配置系統
	統一管理所有調試訊息的顯示設置
]]

local DebugConfig = {}

-- 調試模式設置
local debugSettings = {
	-- 全局調試開關
	enabled = false, -- 設為 false 關閉所有調試訊息
	
	-- 只在 Studio 中啟用調試
	studioOnly = true,
	
	-- 具體模組的調試開關
	targeting = false,      -- 目標搜尋系統
	ai = false,            -- AI更新系統
	combat = false,        -- 戰鬥系統
	health = false,        -- 血量系統
	effects = false,       -- 特效系統
	performance = false,   -- 性能監控
	
	-- 調試訊息頻率控制
	intervals = {
		targeting = 30,    -- 目標搜尋：30秒輸出一次統計
		ai = 10,          -- AI系統：10秒輸出一次統計
		performance = 5,   -- 性能監控：5秒輸出一次
	}
}

-- 檢查是否應該輸出調試訊息
function DebugConfig.shouldLog(category)
	-- 如果全局調試關閉，直接返回 false
	if not debugSettings.enabled then
		return false
	end
	
	-- 如果設置為只在 Studio 中調試，檢查當前環境
	if debugSettings.studioOnly then
		local RunService = game:GetService("RunService")
		if not RunService:IsStudio() then
			return false
		end
	end
	
	-- 檢查具體類別的開關
	if category and debugSettings[category] ~= nil then
		return debugSettings[category]
	end
	
	-- 默認返回全局設置
	return debugSettings.enabled
end

-- 條件性輸出調試訊息
function DebugConfig.log(category, ...)
	if DebugConfig.shouldLog(category) then
		print(...)
	end
end

-- 帶頻率控制的調試訊息
function DebugConfig.logWithInterval(category, key, currentTime, ...)
	if not DebugConfig.shouldLog(category) then
		return false
	end
	
	-- 初始化時間記錄
	if not DebugConfig._lastLogTimes then
		DebugConfig._lastLogTimes = {}
	end
	
	local interval = debugSettings.intervals[category] or 10
	local lastTime = DebugConfig._lastLogTimes[key] or 0
	
	if currentTime - lastTime >= interval then
		DebugConfig._lastLogTimes[key] = currentTime
		print(...)
		return true
	end
	
	return false
end

-- 設置調試開關
function DebugConfig.setDebugEnabled(category, enabled)
	if category == "all" then
		debugSettings.enabled = enabled
	elseif debugSettings[category] ~= nil then
		debugSettings[category] = enabled
	else
		warn("DebugConfig: Unknown category:", category)
	end
end

-- 設置調試間隔
function DebugConfig.setDebugInterval(category, interval)
	if debugSettings.intervals[category] then
		debugSettings.intervals[category] = math.max(1, interval)
	else
		warn("DebugConfig: Unknown interval category:", category)
	end
end

-- 獲取當前調試設置
function DebugConfig.getSettings()
	return {
		enabled = debugSettings.enabled,
		studioOnly = debugSettings.studioOnly,
		categories = {
			targeting = debugSettings.targeting,
			ai = debugSettings.ai,
			combat = debugSettings.combat,
			health = debugSettings.health,
			effects = debugSettings.effects,
			performance = debugSettings.performance,
		},
		intervals = debugSettings.intervals
	}
end

-- 重置所有調試設置
function DebugConfig.reset()
	debugSettings.enabled = false
	debugSettings.targeting = false
	debugSettings.ai = false
	debugSettings.combat = false
	debugSettings.health = false
	debugSettings.effects = false
	debugSettings.performance = false
	
	DebugConfig._lastLogTimes = {}
	print("🔧 Debug settings reset")
end

-- 啟用開發模式（所有調試訊息）
function DebugConfig.enableDeveloperMode()
	debugSettings.enabled = true
	debugSettings.targeting = true
	debugSettings.ai = true
	debugSettings.combat = true
	debugSettings.health = true
	debugSettings.effects = true
	debugSettings.performance = true
	
	print("🔧 Developer mode enabled - All debug messages active")
end

-- 啟用生產模式（關閉所有調試訊息）
function DebugConfig.enableProductionMode()
	debugSettings.enabled = false
	debugSettings.targeting = false
	debugSettings.ai = false
	debugSettings.combat = false
	debugSettings.health = false
	debugSettings.effects = false
	debugSettings.performance = false
	
	print("🔧 Production mode enabled - All debug messages disabled")
end

-- 聊天命令處理（用於運行時調整）
function DebugConfig.handleChatCommand(player, message)
	local args = string.split(message:lower(), " ")
	
	if args[1] == "/debug" then
		if args[2] == "on" then
			DebugConfig.setDebugEnabled("all", true)
			return true
		elseif args[2] == "off" then
			DebugConfig.setDebugEnabled("all", false)
			return true
		elseif args[2] == "dev" then
			DebugConfig.enableDeveloperMode()
			return true
		elseif args[2] == "prod" then
			DebugConfig.enableProductionMode()
			return true
		elseif args[2] == "status" then
			local settings = DebugConfig.getSettings()
			print("🔧 Debug Status:")
			print("  Global:", settings.enabled)
			print("  Studio Only:", settings.studioOnly)
			for category, enabled in pairs(settings.categories) do
				print("  " .. category .. ":", enabled)
			end
			return true
		elseif args[2] and args[3] then
			local category = args[2]
			local enabled = args[3] == "on"
			DebugConfig.setDebugEnabled(category, enabled)
			print("🔧 Debug", category, enabled and "enabled" or "disabled")
			return true
		else
			print("🔧 Debug Commands:")
			print("  /debug on/off - 全局調試開關")
			print("  /debug dev - 開發模式")
			print("  /debug prod - 生產模式")
			print("  /debug status - 查看狀態")
			print("  /debug [category] on/off - 特定類別開關")
			print("  Categories: targeting, ai, combat, health, effects, performance")
			return true
		end
	end
	
	return false
end

-- 自動檢測環境並設置默認模式
function DebugConfig.autoSetup()
	local RunService = game:GetService("RunService")
	
	if RunService:IsStudio() then
		-- Studio 環境：啟用基本調試
		debugSettings.enabled = true
		debugSettings.studioOnly = true
		debugSettings.performance = true
		print("🔧 Debug auto-setup: Studio mode with basic debugging")
	else
		-- 生產環境：關閉所有調試
		DebugConfig.enableProductionMode()
		print("🔧 Debug auto-setup: Production mode")
	end
end

-- 初始化
DebugConfig.autoSetup()

return DebugConfig
