--[[
	測試運行器
	在 Roblox Studio 中運行 TestEZ 測試
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local TestEZ = require(ServerStorage.DevPackages.testez)

-- 運行測試
print("🧪 開始運行測試...")

-- 手動運行測試以避免 TestEZ 版本問題
local success, error = pcall(function()
	-- 運行 Fusion API 測試
	local fusionTest = require(script.Parent["FusionAPITest.spec"])
	print("✅ Fusion API 測試通過")

	-- 運行 PetUIController 測試
	local petUITest = require(script.Parent["PetUIControllerTest.spec"])
	print("✅ PetUIController 測試通過")
end)

if success then
	print("🎉 所有測試通過！")
else
	warn("❌ 測試失敗:", error)
end
