--[[
	客戶端初始化腳本
	整合 Knit 框架和客戶端 UI 系統
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)

-- 載入所有控制器
local Controllers = script.Controllers
for _, controller in pairs(Controllers:GetChildren()) do
	if controller:IsA("ModuleScript") then
		require(controller)
	end
end

-- 啟動 Knit
Knit.Start():andThen(function()
	print("🎮 Client started successfully!")
	print("🎨 Fusion UI system ready")
	
	-- 初始化 UI 系統
	local UIController = Knit.GetController("UIController")
	if UIController then
		UIController:InitializeUI()
	end
	
end):catch(function(err)
	warn("❌ Client startup failed:", err)
end)
