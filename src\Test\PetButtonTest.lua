--[[
	PetButtonTest.lua - 測試寵物召喚按鈕功能
	檢查按鈕點擊、數據載入、信號發送等
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🐾 Pet Button Test Starting...")
print("=" .. string.rep("=", 50))

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待服務初始化
	task.wait(3)
	
	-- 獲取控制器和服務
	local PetController = Knit.GetController("PetController")
	local PetService = Knit.GetService("PetService")
	
	if not PetController then
		warn("❌ PetController not found!")
		return
	end
	
	if not PetService then
		warn("❌ PetService not found!")
		return
	end
	
	print("✅ Controllers and services found")
	
	-- 檢查玩家檔案
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Player profile found")
	print("  Owned pets:", profile.Data.ownedPets)
	
	-- 確保玩家有史萊姆寵物
	if not profile.Data.ownedPets.Slime then
		print("💰 Adding Slime pet for testing...")
		PlayerProfile.addPet(player, "Slime", {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
		print("  Slime pet added")
	end
	
	-- 監聽寵物數據更新
	local dataReceived = false
	PetService.PetDataUpdated:Connect(function(pets, dexData)
		dataReceived = true
		print("✅ Pet data received!")
		print("  Pets:", pets)
		print("  Dex data:", dexData)
	end)
	
	-- 監聽召喚信號
	local summonSignalSent = false
	local originalFire = PetService.SummonPet.Fire
	PetService.SummonPet.Fire = function(self, petId)
		summonSignalSent = true
		print("✅ SummonPet signal fired with petId:", petId)
		return originalFire(self, petId)
	end
	
	-- 監聽召喚結果
	local summonReceived = false
	PetService.PetSummoned:Connect(function(petId)
		summonReceived = true
		print("✅ Pet summoned signal received:", petId)
	end)
	
	-- 請求寵物數據
	print("\n🧪 Step 1: Request pet data...")
	PetService.GetPetDex:Fire()
	
	task.wait(3)
	
	-- 檢查數據是否載入
	if not dataReceived then
		print("❌ Pet data not received, trying manual approach...")
		
		-- 手動設置寵物數據進行測試
		print("💰 Manually setting pet data for testing...")
	end
	
	-- 測試直接調用召喚函數
	print("\n🧪 Step 2: Test direct summon function...")
	
	-- 檢查 PetController 的狀態
	print("PetController methods:")
	for key, value in pairs(PetController) do
		if type(value) == "function" and key:match("Summon") then
			print("  -", key)
		end
	end
	
	-- 嘗試直接調用召喚函數
	if PetController.SummonSelectedPet then
		print("🧪 Calling SummonSelectedPet directly...")
		
		-- 先設置選中的寵物（如果可能的話）
		local success, err = pcall(function()
			PetController:SummonSelectedPet()
		end)
		
		if not success then
			print("❌ Direct summon failed:", err)
		else
			print("✅ Direct summon call succeeded")
		end
	end
	
	task.wait(2)
	
	-- 測試手動發送信號
	print("\n🧪 Step 3: Test manual signal...")
	print("Manually firing SummonPet signal...")
	PetService.SummonPet:Fire("Slime")
	
	task.wait(3)
	
	-- 結果總結
	print("\n" .. string.rep("=", 50))
	print("🧪 TEST RESULTS")
	print(string.rep("=", 50))
	
	print("Data Received:", dataReceived and "✅ PASS" or "❌ FAIL")
	print("Signal Sent:", summonSignalSent and "✅ PASS" or "❌ FAIL")
	print("Summon Received:", summonReceived and "✅ PASS" or "❌ FAIL")
	
	-- 診斷建議
	print("\n🔧 DIAGNOSIS:")
	
	if not dataReceived then
		print("❌ Pet data not loading properly")
		print("   - Check PetService:_sendPetDex method")
		print("   - Check player profile loading timing")
	end
	
	if not summonSignalSent then
		print("❌ Summon button not working")
		print("   - Check UI button click handler")
		print("   - Check selectedPet state")
		print("   - Check ownedPets state")
	end
	
	if summonSignalSent and not summonReceived then
		print("❌ Server not processing summon")
		print("   - Check PetService:_summonPet method")
		print("   - Check player pet ownership")
		print("   - Check Matter World connection")
	end
	
	if dataReceived and summonSignalSent and summonReceived then
		print("✅ Pet summoning system is working!")
		print("   If button still not working, check:")
		print("   - UI state management")
		print("   - Pet selection logic")
	end
	
	print(string.rep("=", 50))
	
end):catch(function(err)
	warn("❌ Test failed with error:", err)
end)

return true
