--[[
	Utils - 共享工具函數
	提供遊戲中常用的工具函數
]]

local Utils = {}

-- 數學工具
function Utils.lerp(a, b, t)
	return a + (b - a) * t
end

function Utils.clamp(value, min, max)
	return math.max(min, math.min(max, value))
end

function Utils.round(number, decimals)
	local multiplier = 10 ^ (decimals or 0)
	return math.floor(number * multiplier + 0.5) / multiplier
end

-- 隨機工具
function Utils.randomChoice(array)
	if #array == 0 then return nil end
	return array[math.random(1, #array)]
end

function Utils.weightedRandom(weights)
	local totalWeight = 0
	for _, weight in pairs(weights) do
		totalWeight = totalWeight + weight
	end
	
	local random = math.random() * totalWeight
	local currentWeight = 0
	
	for key, weight in pairs(weights) do
		currentWeight = currentWeight + weight
		if random <= currentWeight then
			return key
		end
	end
	
	return next(weights) -- 備用返回
end

-- 字符串工具
function Utils.formatNumber(number)
	if number >= 1000000 then
		return string.format("%.1fM", number / 1000000)
	elseif number >= 1000 then
		return string.format("%.1fK", number / 1000)
	else
		return tostring(number)
	end
end

function Utils.formatTime(seconds)
	local hours = math.floor(seconds / 3600)
	local minutes = math.floor((seconds % 3600) / 60)
	local secs = seconds % 60
	
	if hours > 0 then
		return string.format("%02d:%02d:%02d", hours, minutes, secs)
	else
		return string.format("%02d:%02d", minutes, secs)
	end
end

-- 表格工具
function Utils.deepCopy(original)
	local copy = {}
	for key, value in pairs(original) do
		if type(value) == "table" then
			copy[key] = Utils.deepCopy(value)
		else
			copy[key] = value
		end
	end
	return copy
end

function Utils.merge(target, source)
	for key, value in pairs(source) do
		if type(value) == "table" and type(target[key]) == "table" then
			Utils.merge(target[key], value)
		else
			target[key] = value
		end
	end
	return target
end

-- 顏色工具
function Utils.hexToColor3(hex)
	hex = hex:gsub("#", "")
	local r = tonumber(hex:sub(1, 2), 16) / 255
	local g = tonumber(hex:sub(3, 4), 16) / 255
	local b = tonumber(hex:sub(5, 6), 16) / 255
	return Color3.new(r, g, b)
end

function Utils.lerpColor3(color1, color2, t)
	return Color3.new(
		Utils.lerp(color1.R, color2.R, t),
		Utils.lerp(color1.G, color2.G, t),
		Utils.lerp(color1.B, color2.B, t)
	)
end

-- 驗證工具
function Utils.isValidPlayer(player)
	return player and player.Parent == game:GetService("Players")
end

function Utils.isValidCharacter(character)
	return character and character.Parent and character:FindFirstChild("Humanoid")
end

return Utils
