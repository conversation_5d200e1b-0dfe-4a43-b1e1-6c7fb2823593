--[[
	GachaTest.lua - 抽卡功能測試腳本
	用於測試單抽和十連抽功能
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 等待玩家加入
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("🧪 Starting Gacha Test...")
	
	-- 獲取 GachaService
	local GachaService = Knit.GetService("GachaService")
	
	if not GachaService then
		warn("❌ GachaService not found!")
		return
	end
	
	print("✅ GachaService found")
	
	-- 監聽抽卡結果
	GachaService.GachaResult:Connect(function(results, poolType, isTenPull)
		print("🎰 Test - Gacha result received:")
		print("  Results count:", #results)
		print("  Pool type:", poolType)
		print("  Is ten pull:", isTenPull)
		
		for i, result in ipairs(results) do
			print("  Result", i .. ":", result.name, "(" .. result.rarity .. ")")
		end
	end)
	
	-- 監聽金幣不足
	GachaService.InsufficientFunds:Connect(function(required, current)
		print("💰 Test - Insufficient funds:")
		print("  Required:", required)
		print("  Current:", current)
	end)
	
	-- 監聽抽卡數據更新
	GachaService.GachaDataUpdated:Connect(function(data)
		print("📊 Test - Gacha data updated:")
		print("  History count:", #(data.history or {}))
		print("  Costs:", data.costs)
	end)
	
	-- 等待一段時間後開始測試
	task.wait(2)
	
	print("\n🧪 Testing Single Pull...")
	GachaService.SinglePull:Fire("pet")
	
	-- 等待單抽完成後測試十連抽
	task.wait(3)
	
	print("\n🧪 Testing Ten Pull...")
	GachaService.TenPull:Fire("pet")
	
	-- 等待十連抽完成
	task.wait(5)
	
	print("\n🧪 Gacha Test Completed!")
end):catch(function(err)
	warn("❌ Gacha Test Error:", err)
end)

return true
