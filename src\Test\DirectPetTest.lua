--[[
	DirectPetTest.lua - 直接測試寵物召喚功能
	繞過 UI，直接測試核心功能
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🐾 Direct Pet Test Starting...")
print("=" .. string.rep("=", 50))

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待服務初始化
	task.wait(3)
	
	-- 獲取服務
	local PetService = Knit.GetService("PetService")
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	if not PetService then
		warn("❌ PetService not found!")
		return
	end
	
	print("✅ PetService found")
	
	-- 等待玩家檔案載入
	local profile
	local attempts = 0
	while attempts < 20 do
		profile = PlayerProfile.getProfile(player)
		if profile then
			break
		end
		attempts = attempts + 1
		task.wait(0.5)
	end
	
	if not profile then
		warn("❌ Player profile not found after 10 seconds!")
		return
	end
	
	print("✅ Player profile found")
	print("  Current pets:", profile.Data.ownedPets)
	
	-- 添加測試寵物
	if not profile.Data.ownedPets.Slime then
		print("💰 Adding Slime pet...")
		PlayerProfile.addPet(player, "Slime", {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
		print("  Slime pet added")
	end
	
	-- 監聽召喚事件
	local summonReceived = false
	PetService.PetSummoned:Connect(function(petId)
		summonReceived = true
		print("🎉 Pet summoned signal received:", petId)
		
		-- 檢查 ECS 實體
		if _G.MatterWorld then
			task.spawn(function()
				task.wait(1)
				local PetComponent = require(game:GetService("ReplicatedStorage").ECS.Components.PetComponent)
				for entityId, pet in _G.MatterWorld:query(PetComponent) do
					if pet.petId == petId then
						print("✅ Pet entity found in ECS:", entityId)
						print("  Pet data:", pet)
						return
					end
				end
				print("❌ Pet entity not found in ECS")
			end)
		end
		
		-- 檢查 workspace 模型
		task.spawn(function()
			for i = 1, 10 do
				for _, child in ipairs(workspace:GetChildren()) do
					if child.Name:match("Pet_") then
						print("✅ Pet model found:", child.Name)
						if child.PrimaryPart then
							print("  Position:", child.PrimaryPart.Position)
						end
						return
					end
				end
				task.wait(0.5)
			end
			print("❌ No pet model found in workspace")
		end)
	end)
	
	-- 監聽寵物數據更新
	local dataReceived = false
	PetService.PetDataUpdated:Connect(function(pets, dexData)
		dataReceived = true
		print("✅ Pet data received!")
		print("  Pets:", pets)
		print("  Dex data count:", dexData and #dexData or 0)
	end)
	
	-- 測試序列
	print("\n🧪 Step 1: Request pet data...")
	PetService.GetPetDex:Fire()
	
	task.wait(3)
	
	if not dataReceived then
		print("❌ Pet data not received, but continuing with test...")
	end
	
	print("\n🧪 Step 2: Test direct summon...")
	print("Firing SummonPet signal for Slime...")
	PetService.SummonPet:Fire("Slime")
	
	task.wait(5)
	
	-- 測試不同寵物
	local testPets = {"Wolf", "FireSpirit"}
	for _, petId in ipairs(testPets) do
		if not profile.Data.ownedPets[petId] then
			print("💰 Adding", petId, "pet...")
			PlayerProfile.addPet(player, petId, {
				level = 1,
				experience = 0,
				rarity = "Common",
				obtainedTime = os.time(),
			})
		end
		
		print("\n🧪 Testing", petId, "summon...")
		PetService.SummonPet:Fire(petId)
		task.wait(3)
	end
	
	-- 結果總結
	print("\n" .. string.rep("=", 50))
	print("🧪 TEST RESULTS")
	print(string.rep("=", 50))
	
	print("Data Received:", dataReceived and "✅ PASS" or "❌ FAIL")
	print("Summon Received:", summonReceived and "✅ PASS" or "❌ FAIL")
	
	-- 檢查系統狀態
	print("\n🔍 SYSTEM STATUS:")
	print("Matter World:", _G.MatterWorld and "✅ Available" or "❌ Missing")
	
	if _G.MatterWorld then
		local entityCount = 0
		for _ in _G.MatterWorld:query() do
			entityCount = entityCount + 1
		end
		print("ECS Entities:", entityCount)
	end
	
	-- 檢查玩家實體
	if _G.MatterWorld then
		local PlayerComponent = require(game:GetService("ReplicatedStorage").ECS.Components.PlayerComponent)
		local playerEntityFound = false
		for entityId, playerComp in _G.MatterWorld:query(PlayerComponent) do
			if playerComp.userId == player.UserId then
				playerEntityFound = true
				print("Player Entity:", entityId)
				break
			end
		end
		print("Player Entity Found:", playerEntityFound and "✅ YES" or "❌ NO")
	end
	
	-- 診斷建議
	print("\n🔧 RECOMMENDATIONS:")
	
	if not dataReceived then
		print("❌ Fix pet data loading:")
		print("   - Check PetService GetPetDex signal connection")
		print("   - Check _sendPetDex method")
		print("   - Check player profile loading timing")
	end
	
	if not summonReceived then
		print("❌ Fix pet summoning:")
		print("   - Check PetService SummonPet signal connection")
		print("   - Check _summonPet method implementation")
		print("   - Check pet ownership validation")
	end
	
	if summonReceived then
		print("✅ Core summoning works!")
		print("   If UI button doesn't work, check:")
		print("   - Button click handler")
		print("   - Pet selection state")
		print("   - UI data binding")
	end
	
	print(string.rep("=", 50))
	
end):catch(function(err)
	warn("❌ Test failed with error:", err)
end)

return true
