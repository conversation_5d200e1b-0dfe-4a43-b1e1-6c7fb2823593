--[[
	UIController - UI 管理控制器
	使用 Fusion 框架管理用戶界面
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)
local Players = game:GetService("Players")

-- Fusion 組件
local New = Fusion.New
local Value = Fusion.Value
local Computed = Fusion.Computed

local UIController = Knit.CreateController({
	Name = "UIController",
})

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local mainGui

function UIController:KnitStart()
	print("🎨 UIController started")
	
	-- 創建主 UI
	self:_createMainUI()
end

function UIController:KnitInit()
	-- 初始化控制器依賴
end

-- 創建主要 UI
function UIController:_createMainUI()
	-- 創建狀態
	local coinsState = Value(0)
	local levelState = Value(1)
	
	-- 創建主 GUI
	mainGui = New "ScreenGui" {
		Name = "PetRPG_MainUI",
		Parent = playerGui,
		ResetOnSpawn = false,
		
		[Fusion.Children] = {
			-- 頂部狀態欄
			New "Frame" {
				Name = "TopBar",
				Size = UDim2.new(1, 0, 0, 60),
				Position = UDim2.new(0, 0, 0, 0),
				BackgroundColor3 = Color3.fromRGB(30, 30, 30),
				BorderSizePixel = 0,
				
				[Fusion.Children] = {
					-- 金幣顯示
					New "Frame" {
						Name = "CoinsFrame",
						Size = UDim2.new(0, 150, 1, -10),
						Position = UDim2.new(0, 10, 0, 5),
						BackgroundColor3 = Color3.fromRGB(255, 215, 0),
						BorderSizePixel = 0,
						
						[Fusion.Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 8),
							},
							
							New "TextLabel" {
								Name = "CoinsLabel",
								Size = UDim2.new(1, -10, 1, 0),
								Position = UDim2.new(0, 5, 0, 0),
								BackgroundTransparency = 1,
								Text = Computed(function()
									return "💰 " .. coinsState:get()
								end),
								TextColor3 = Color3.fromRGB(0, 0, 0),
								TextScaled = true,
								Font = Enum.Font.GothamBold,
							},
						},
					},
					
					-- 等級顯示
					New "Frame" {
						Name = "LevelFrame",
						Size = UDim2.new(0, 100, 1, -10),
						Position = UDim2.new(0, 170, 0, 5),
						BackgroundColor3 = Color3.fromRGB(100, 200, 255),
						BorderSizePixel = 0,
						
						[Fusion.Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 8),
							},
							
							New "TextLabel" {
								Name = "LevelLabel",
								Size = UDim2.new(1, -10, 1, 0),
								Position = UDim2.new(0, 5, 0, 0),
								BackgroundTransparency = 1,
								Text = Computed(function()
									return "⭐ Lv." .. levelState:get()
								end),
								TextColor3 = Color3.fromRGB(255, 255, 255),
								TextScaled = true,
								Font = Enum.Font.GothamBold,
							},
						},
					},
				},
			},
		},
	}
	
	-- 存儲狀態引用以便後續更新
	self.coinsState = coinsState
	self.levelState = levelState
end

-- 更新金幣顯示
function UIController:UpdateCoins(amount)
	if self.coinsState then
		self.coinsState:set(amount)
	end
end

-- 更新等級顯示
function UIController:UpdateLevel(level)
	if self.levelState then
		self.levelState:set(level)
	end
end

return UIController
