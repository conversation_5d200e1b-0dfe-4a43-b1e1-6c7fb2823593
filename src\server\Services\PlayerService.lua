--[[
	PlayerService - 玩家管理服務（重構版）
	整合 Matter ECS 和 ProfileService 的玩家管理
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Players = game:GetService("Players")
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local PlayerService = Knit.CreateService({
	Name = "PlayerService",
	Client = {
		-- 客戶端可調用的方法
		GetPlayerData = Knit.CreateSignal(),
		PlayerDataUpdated = Knit.CreateSignal(),
	},
})

-- 私有變量
local playerEntities = {} -- 玩家實體映射 {[player] = entityId}

function PlayerService:KnitStart()
	print("🎯 PlayerService started (ECS Version)")
	
	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found!")
		return
	end
	
	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:_onPlayerAdded(player)
	end)
	
	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		self:_onPlayerRemoving(player)
	end)
	
	-- 處理已經在遊戲中的玩家
	for _, player in pairs(Players:GetPlayers()) do
		self:_onPlayerAdded(player)
	end
end

function PlayerService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
end

-- 玩家加入處理
function PlayerService:_onPlayerAdded(player)
	print("👋 Player joined:", player.Name)
	
	-- 等待角色生成
	if not player.Character then
		player.CharacterAdded:Wait()
	end
	
	-- 創建玩家實體
	local entityId = self:_createPlayerEntity(player)
	playerEntities[player] = entityId
	
	-- 載入玩家數據
	if self.DataService then
		self.DataService:LoadPlayerData(player):andThen(function(playerData)
			self:_updatePlayerEntityData(entityId, playerData)
			
			-- 通知客戶端
			self.Client.GetPlayerData:Fire(player, playerData)
		end):catch(function(err)
			warn("❌ Failed to load player data:", err)
		end)
	end
end

-- 玩家離開處理
function PlayerService:_onPlayerRemoving(player)
	print("👋 Player leaving:", player.Name)
	
	-- 移除玩家實體
	local entityId = playerEntities[player]
	if entityId and self.world then
		self.world:despawn(entityId)
		playerEntities[player] = nil
	end
	
	-- 保存玩家數據
	if self.DataService then
		self.DataService:SavePlayerData(player)
	end
end

-- 創建玩家實體
function PlayerService:_createPlayerEntity(player)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		warn("❌ Player character not ready:", player.Name)
		return nil
	end
	
	local character = player.Character
	local humanoidRootPart = character.HumanoidRootPart
	
	-- 創建玩家實體
	local entityId = self.world:spawn(
		Components.Player({
			userId = player.UserId,
			displayName = player.DisplayName,
			level = 1,
			experience = 0,
			coins = 0,
		}),
		Components.Position({
			position = humanoidRootPart.Position,
			rotation = humanoidRootPart.CFrame,
		}),
		Components.Health({
			current = 100,
			maximum = 100,
			regeneration = 1, -- 每秒回血1點
		}),
		Components.Damage({
			attack = 20,
			defense = 5,
			criticalChance = 0.1,
			criticalMultiplier = 2.0,
		}),
		Components.Movement({
			velocity = Vector3.new(),
			speed = 16,
			acceleration = 50,
			friction = 0.9,
		})
	)
	
	print("🎯 Created player entity:", entityId, "for", player.Name)
	return entityId
end

-- 更新玩家實體數據
function PlayerService:_updatePlayerEntityData(entityId, playerData)
	if not self.world or not entityId then return end
	
	local playerComponent = self.world:get(entityId, Components.Player)
	if playerComponent then
		self.world:insert(entityId, playerComponent:patch({
			level = playerData.level or 1,
			experience = playerData.experience or 0,
			coins = playerData.coins or 0,
		}))
	end
	
	-- 更新血量和傷害數據
	local healthComponent = self.world:get(entityId, Components.Health)
	if healthComponent and playerData.stats then
		self.world:insert(entityId, healthComponent:patch({
			maximum = playerData.stats.maxHealth or 100,
			current = playerData.stats.currentHealth or playerData.stats.maxHealth or 100,
		}))
	end
	
	local damageComponent = self.world:get(entityId, Components.Damage)
	if damageComponent and playerData.stats then
		self.world:insert(entityId, damageComponent:patch({
			attack = playerData.stats.attack or 20,
			defense = playerData.stats.defense or 5,
		}))
	end
end

-- 獲取玩家實體ID
function PlayerService:GetPlayerEntityId(player)
	return playerEntities[player]
end

-- 獲取玩家數據
function PlayerService:GetPlayerData(player)
	local entityId = playerEntities[player]
	if not entityId or not self.world then return nil end
	
	local playerComponent = self.world:get(entityId, Components.Player)
	local healthComponent = self.world:get(entityId, Components.Health)
	local damageComponent = self.world:get(entityId, Components.Damage)
	
	return {
		player = playerComponent,
		health = healthComponent,
		damage = damageComponent,
	}
end

-- 更新玩家位置（從角色同步到實體）
function PlayerService:UpdatePlayerPosition(player)
	local entityId = playerEntities[player]
	if not entityId or not self.world then return end
	
	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		local humanoidRootPart = player.Character.HumanoidRootPart
		local positionComponent = self.world:get(entityId, Components.Position)
		
		if positionComponent then
			self.world:insert(entityId, positionComponent:patch({
				position = humanoidRootPart.Position,
				rotation = humanoidRootPart.CFrame,
			}))
		end
	end
end

return PlayerService
