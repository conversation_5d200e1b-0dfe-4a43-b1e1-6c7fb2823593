--[[
	AddCoins.lua - 給玩家添加金幣的測試腳本
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("💰 Adding coins to player:", player.Name)

-- 獲取 PlayerProfile
local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)

-- 檢查當前金幣
local profile = PlayerProfile.getProfile(player)
if profile then
	print("  Current coins:", profile.Data.coins)
	
	-- 如果金幣不足，添加金幣
	if profile.Data.coins < 2000 then
		local coinsToAdd = 2000 - profile.Data.coins
		local newBalance = PlayerProfile.addCoins(player, coinsToAdd)
		print("  Added", coinsToAdd, "coins")
		print("  New balance:", newBalance)
	else
		print("  Player already has enough coins!")
	end
else
	warn("❌ Player profile not found!")
end

return true
