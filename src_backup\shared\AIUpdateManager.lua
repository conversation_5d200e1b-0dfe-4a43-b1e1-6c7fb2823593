--[[
	AIUpdateManager - 優化的AI更新管理器
	實現時間片輪詢和距離優化的AI更新系統
]]

local AIUpdateManager = {}
local DebugConfig = require(script.Parent.DebugConfig)

-- 私有變量
local aiEntities = {} -- 所有需要AI更新的實體
local updateIndex = 1 -- 當前更新索引
local maxUpdatesPerFrame = 5 -- 每幀最大更新數量
local lastFrameTime = 0
local frameTimeThreshold = 1/60 -- 60fps目標

-- AI更新優先級
local PRIORITY_HIGH = 1    -- 近距離，每幀更新
local PRIORITY_MEDIUM = 2  -- 中距離，每2幀更新
local PRIORITY_LOW = 3     -- 遠距離，每4幀更新
local PRIORITY_VERY_LOW = 4 -- 極遠距離，每8幀更新

-- 距離閾值
local DISTANCE_THRESHOLDS = {
	HIGH = 15,      -- 15格內高優先級
	MEDIUM = 30,    -- 30格內中優先級
	LOW = 50,       -- 50格內低優先級
	VERY_LOW = 100  -- 100格內極低優先級
}

-- 註冊AI實體
function AIUpdateManager.registerEntity(instanceId, updateFunction, position)
	if not instanceId or not updateFunction then
		warn("AIUpdateManager: Invalid parameters for registerEntity")
		return
	end
	
	aiEntities[instanceId] = {
		updateFunction = updateFunction,
		position = position or Vector3.new(0, 0, 0),
		priority = PRIORITY_MEDIUM,
		lastUpdate = 0,
		frameSkipCount = 0,
		isActive = true
	}
	
	-- 使用調試配置輸出註冊信息
	DebugConfig.log("ai", "🤖 AI Entity registered:", instanceId)
end

-- 移除AI實體
function AIUpdateManager.unregisterEntity(instanceId)
	if aiEntities[instanceId] then
		aiEntities[instanceId] = nil
		-- 使用調試配置輸出註銷信息
		DebugConfig.log("ai", "🤖 AI Entity unregistered:", instanceId)
	end
end

-- 更新實體位置
function AIUpdateManager.updateEntityPosition(instanceId, position)
	local entity = aiEntities[instanceId]
	if entity then
		entity.position = position
	end
end

-- 計算AI優先級
local function calculatePriority(entityPosition, playerPositions)
	local minDistance = math.huge
	
	-- 找到最近的玩家距離
	for _, playerPos in pairs(playerPositions) do
		local distance = (entityPosition - playerPos).Magnitude
		if distance < minDistance then
			minDistance = distance
		end
	end
	
	-- 根據距離確定優先級
	if minDistance <= DISTANCE_THRESHOLDS.HIGH then
		return PRIORITY_HIGH
	elseif minDistance <= DISTANCE_THRESHOLDS.MEDIUM then
		return PRIORITY_MEDIUM
	elseif minDistance <= DISTANCE_THRESHOLDS.LOW then
		return PRIORITY_LOW
	else
		return PRIORITY_VERY_LOW
	end
end

-- 檢查是否應該更新
local function shouldUpdate(entity, frameCount)
	local skipFrames = 0
	
	if entity.priority == PRIORITY_HIGH then
		skipFrames = 0 -- 每幀更新
	elseif entity.priority == PRIORITY_MEDIUM then
		skipFrames = 1 -- 每2幀更新
	elseif entity.priority == PRIORITY_LOW then
		skipFrames = 3 -- 每4幀更新
	else -- PRIORITY_VERY_LOW
		skipFrames = 7 -- 每8幀更新
	end
	
	entity.frameSkipCount = entity.frameSkipCount + 1
	
	if entity.frameSkipCount > skipFrames then
		entity.frameSkipCount = 0
		return true
	end
	
	return false
end

-- 獲取所有玩家位置
local function getPlayerPositions()
	local positions = {}
	local Players = game:GetService("Players")
	
	for _, player in pairs(Players:GetPlayers()) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			table.insert(positions, player.Character.HumanoidRootPart.Position)
		end
	end
	
	return positions
end

-- 主更新函數
function AIUpdateManager.update()
	local currentTime = tick()
	local frameTime = currentTime - lastFrameTime
	lastFrameTime = currentTime
	
	-- 如果幀時間過長，減少更新數量
	local updatesThisFrame = maxUpdatesPerFrame
	if frameTime > frameTimeThreshold * 1.5 then
		updatesThisFrame = math.max(1, math.floor(maxUpdatesPerFrame * 0.5))
	end
	
	local playerPositions = getPlayerPositions()
	local entityList = {}
	
	-- 轉換為數組並更新優先級
	for instanceId, entity in pairs(aiEntities) do
		if entity.isActive then
			entity.priority = calculatePriority(entity.position, playerPositions)
			table.insert(entityList, {id = instanceId, entity = entity})
		end
	end
	
	-- 按優先級排序
	table.sort(entityList, function(a, b)
		return a.entity.priority < b.entity.priority
	end)
	
	-- 時間片更新
	local updatesPerformed = 0
	local totalEntities = #entityList
	
	if totalEntities > 0 then
		for i = 1, math.min(updatesThisFrame, totalEntities) do
			local index = ((updateIndex - 1 + i - 1) % totalEntities) + 1
			local entityData = entityList[index]
			local entity = entityData.entity
			
			if shouldUpdate(entity, currentTime) then
				-- 執行AI更新
				local success, err = pcall(entity.updateFunction, entityData.id, entity)
				if not success then
					warn("AIUpdateManager: Error updating entity", entityData.id, ":", err)
				end
				
				entity.lastUpdate = currentTime
				updatesPerformed = updatesPerformed + 1
			end
		end
		
		-- 更新索引
		updateIndex = (updateIndex + updatesThisFrame - 1) % totalEntities + 1
	end
	
	-- 使用調試配置輸出統計信息
	if totalEntities > 0 then
		DebugConfig.logWithInterval("ai", "update_stats", currentTime,
			"🤖 AI Update Stats - Entities:", totalEntities, "Updates:", updatesPerformed, "Frame time:", math.floor(frameTime * 1000) .. "ms")
	end
end

-- 設置每幀最大更新數量
function AIUpdateManager.setMaxUpdatesPerFrame(count)
	maxUpdatesPerFrame = math.max(1, count)
end

-- 設置幀時間閾值
function AIUpdateManager.setFrameTimeThreshold(threshold)
	frameTimeThreshold = math.max(1/120, threshold) -- 最小120fps
end

-- 暫停/恢復實體更新
function AIUpdateManager.setEntityActive(instanceId, active)
	local entity = aiEntities[instanceId]
	if entity then
		entity.isActive = active
	end
end

-- 獲取統計信息
function AIUpdateManager.getStats()
	local activeCount = 0
	local totalCount = 0
	local priorityCount = {0, 0, 0, 0}
	
	for _, entity in pairs(aiEntities) do
		totalCount = totalCount + 1
		if entity.isActive then
			activeCount = activeCount + 1
			priorityCount[entity.priority] = priorityCount[entity.priority] + 1
		end
	end
	
	return {
		total = totalCount,
		active = activeCount,
		maxUpdatesPerFrame = maxUpdatesPerFrame,
		priorityDistribution = priorityCount,
		frameTimeThreshold = frameTimeThreshold
	}
end

-- 清理所有實體
function AIUpdateManager.clear()
	aiEntities = {}
	updateIndex = 1
	print("🤖 AI Update Manager cleared")
end

return AIUpdateManager
