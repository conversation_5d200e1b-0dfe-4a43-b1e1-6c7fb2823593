--[[
	GameController - 遊戲主控制器
	協調客戶端的各種系統和服務通信
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

local GameController = Knit.CreateController({
	Name = "GameController",
})

-- 私有變量
local player = Players.LocalPlayer
local playerData = {}

function GameController:KnitStart()
	print("🎮 GameController started")
	
	-- 獲取服務引用
	self.PlayerService = Knit.GetService("PlayerService")
	
	-- 監聽服務端事件
	self:_connectToServices()
	
	-- 初始化遊戲狀態
	self:_initializeGame()
end

function GameController:KnitInit()
	-- 初始化控制器依賴
end

-- 連接到服務端服務
function GameController:_connectToServices()
	-- 監聽玩家數據更新
	self.PlayerService.PlayerDataUpdated:Connect(function(data)
		self:_onPlayerDataReceived(data)
	end)
end

-- 初始化遊戲
function GameController:_initializeGame()
	-- 等待角色生成
	if player.Character then
		self:_onCharacterAdded(player.Character)
	end
	
	player.CharacterAdded:Connect(function(character)
		self:_onCharacterAdded(character)
	end)
end

-- 角色生成處理
function GameController:_onCharacterAdded(character)
	print("🚶 Character spawned for:", player.Name)
	
	-- 等待人形模型載入
	local humanoid = character:WaitForChild("Humanoid")
	
	-- 設置基本屬性
	humanoid.WalkSpeed = 16
	humanoid.JumpPower = 50
	
	-- 可以在這裡添加更多角色初始化邏輯
end

-- 接收玩家數據
function GameController:_onPlayerDataReceived(data)
	playerData = data
	print("📊 Player data received:", data)
	
	-- 更新 UI
	local UIController = Knit.GetController("UIController")
	if UIController then
		-- 這裡可以根據實際數據結構更新 UI
		-- UIController:UpdateCoins(data.coins or 0)
		-- UIController:UpdateLevel(data.level or 1)
	end
end

-- 獲取本地玩家數據
function GameController:GetPlayerData()
	return playerData
end

return GameController
