--[[
	DevTools.lua - 開發者工具
	提供開發和調試時的便利功能
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local DevTools = {}

-- 工具配置
local TOOLS_CONFIG = {
	enabled = true,
	showCommands = true,
	allowCheats = true, -- 僅在開發環境中啟用
}

-- 命令列表
local COMMANDS = {
	-- 玩家相關
	{
		name = "heal",
		description = "治療玩家到滿血",
		usage = "/heal [amount]",
		func = function(player, args)
			local amount = tonumber(args[1]) or 999
			-- 這裡需要調用實際的治療邏輯
			print("🩹 Healed", player.Name, "for", amount, "HP")
		end,
	},
	
	{
		name = "level",
		description = "設置玩家等級",
		usage = "/level <level>",
		func = function(player, args)
			local level = tonumber(args[1])
			if not level then
				warn("❌ Invalid level:", args[1])
				return
			end
			-- 這裡需要調用實際的等級設置邏輯
			print("📈 Set", player.Name, "level to", level)
		end,
	},
	
	{
		name = "coins",
		description = "給予玩家金幣",
		usage = "/coins <amount>",
		func = function(player, args)
			local amount = tonumber(args[1])
			if not amount then
				warn("❌ Invalid amount:", args[1])
				return
			end
			-- 這裡需要調用實際的金幣給予邏輯
			print("💰 Gave", player.Name, amount, "coins")
		end,
	},
	
	-- 寵物相關
	{
		name = "summon",
		description = "召喚指定寵物",
		usage = "/summon <petId>",
		func = function(player, args)
			local petId = args[1]
			if not petId then
				warn("❌ Pet ID required")
				return
			end
			-- 這裡需要調用實際的寵物召喚邏輯
			print("🐾 Summoned", petId, "for", player.Name)
		end,
	},
	
	{
		name = "recall",
		description = "收回當前寵物",
		usage = "/recall",
		func = function(player, args)
			-- 這裡需要調用實際的寵物收回邏輯
			print("🐾 Recalled pet for", player.Name)
		end,
	},
	
	-- 抽卡相關
	{
		name = "gacha",
		description = "執行抽卡",
		usage = "/gacha [count] [pool]",
		func = function(player, args)
			local count = tonumber(args[1]) or 1
			local pool = args[2] or "standard_pet"
			-- 這裡需要調用實際的抽卡邏輯
			print("🎰 Performed", count, "gacha pulls from", pool, "for", player.Name)
		end,
	},
	
	-- 調試相關
	{
		name = "debug",
		description = "切換調試模式",
		usage = "/debug",
		func = function(player, args)
			local MatterDebugView = require(script.Parent.MatterDebugView)
			MatterDebugView.toggle()
		end,
	},
	
	{
		name = "test",
		description = "運行測試",
		usage = "/test [testName]",
		func = function(player, args)
			local testName = args[1]
			local TestRunner = require(game:GetService("ReplicatedStorage").Test.TestRunner)
			
			if testName then
				TestRunner.runSpecificTest(testName)
			else
				TestRunner.runAllTests()
			end
		end,
	},
	
	{
		name = "spawn",
		description = "生成怪物",
		usage = "/spawn <monsterId> [level]",
		func = function(player, args)
			local monsterId = args[1]
			local level = tonumber(args[2]) or 1
			
			if not monsterId then
				warn("❌ Monster ID required")
				return
			end
			
			-- 這裡需要調用實際的怪物生成邏輯
			print("👹 Spawned", monsterId, "level", level, "near", player.Name)
		end,
	},
	
	-- 系統相關
	{
		name = "help",
		description = "顯示所有可用命令",
		usage = "/help",
		func = function(player, args)
			DevTools.showHelp(player)
		end,
	},
	
	{
		name = "clear",
		description = "清除控制台輸出",
		usage = "/clear",
		func = function(player, args)
			-- 清除控制台（如果可能的話）
			for i = 1, 50 do
				print("")
			end
			print("🧹 Console cleared")
		end,
	},
}

-- 初始化開發者工具
function DevTools.initialize()
	if not TOOLS_CONFIG.enabled then
		return
	end
	
	print("🔧 DevTools initialized")
	
	-- 設置命令處理
	DevTools.setupCommandHandler()
	
	-- 設置快捷鍵
	DevTools.setupHotkeys()
	
	-- 顯示歡迎信息
	DevTools.showWelcome()
end

-- 設置命令處理器
function DevTools.setupCommandHandler()
	Players.PlayerAdded:Connect(function(player)
		player.Chatted:Connect(function(message)
			if message:sub(1, 1) == "/" then
				DevTools.handleCommand(player, message)
			end
		end)
	end)
	
	-- 處理已經在遊戲中的玩家
	for _, player in ipairs(Players:GetPlayers()) do
		player.Chatted:Connect(function(message)
			if message:sub(1, 1) == "/" then
				DevTools.handleCommand(player, message)
			end
		end)
	end
end

-- 設置快捷鍵
function DevTools.setupHotkeys()
	local UserInputService = game:GetService("UserInputService")
	
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		-- F1 - 顯示幫助
		if input.KeyCode == Enum.KeyCode.F1 then
			DevTools.showHelp(Players.LocalPlayer)
		end
		
		-- F2 - 切換調試視圖
		if input.KeyCode == Enum.KeyCode.F2 then
			local MatterDebugView = require(script.Parent.MatterDebugView)
			MatterDebugView.toggle()
		end
		
		-- F3 - 運行測試
		if input.KeyCode == Enum.KeyCode.F3 then
			local TestRunner = require(game:GetService("ReplicatedStorage").Test.TestRunner)
			TestRunner.runAllTests()
		end
	end)
end

-- 處理命令
function DevTools.handleCommand(player, message)
	if not TOOLS_CONFIG.enabled then
		return
	end
	
	-- 解析命令
	local parts = {}
	for part in message:gmatch("%S+") do
		table.insert(parts, part)
	end
	
	local commandName = parts[1]:sub(2) -- 移除 "/"
	local args = {}
	for i = 2, #parts do
		table.insert(args, parts[i])
	end
	
	-- 查找並執行命令
	for _, command in ipairs(COMMANDS) do
		if command.name == commandName then
			local success, error = pcall(command.func, player, args)
			if not success then
				warn("❌ Command error:", error)
			end
			return
		end
	end
	
	-- 命令未找到
	warn("❌ Unknown command:", commandName)
	print("💡 Type /help for available commands")
end

-- 顯示幫助
function DevTools.showHelp(player)
	print("\n🔧 DEVELOPER TOOLS - AVAILABLE COMMANDS:")
	print("=" .. string.rep("=", 50))
	
	for _, command in ipairs(COMMANDS) do
		print(string.format("%-15s - %s", command.usage, command.description))
	end
	
	print("\n🎮 HOTKEYS:")
	print("F1 - Show this help")
	print("F2 - Toggle debug view")
	print("F3 - Run tests")
	
	print("=" .. string.rep("=", 50))
end

-- 顯示歡迎信息
function DevTools.showWelcome()
	print("\n🎮 BATTLE SYSTEM DEVELOPMENT MODE")
	print("=" .. string.rep("=", 40))
	print("🔧 Developer tools are active!")
	print("💡 Type /help for available commands")
	print("🎮 Press F1 for hotkey help")
	print("=" .. string.rep("=", 40))
end

-- 添加自定義命令
function DevTools.addCommand(commandData)
	if not commandData.name or not commandData.func then
		warn("❌ Invalid command data")
		return false
	end
	
	-- 檢查命令是否已存在
	for _, command in ipairs(COMMANDS) do
		if command.name == commandData.name then
			warn("❌ Command already exists:", commandData.name)
			return false
		end
	end
	
	table.insert(COMMANDS, commandData)
	print("✅ Added custom command:", commandData.name)
	return true
end

-- 移除命令
function DevTools.removeCommand(commandName)
	for i, command in ipairs(COMMANDS) do
		if command.name == commandName then
			table.remove(COMMANDS, i)
			print("✅ Removed command:", commandName)
			return true
		end
	end
	
	warn("❌ Command not found:", commandName)
	return false
end

-- 設置配置
function DevTools.setConfig(config)
	for key, value in pairs(config) do
		if TOOLS_CONFIG[key] ~= nil then
			TOOLS_CONFIG[key] = value
		end
	end
end

-- 獲取配置
function DevTools.getConfig()
	return TOOLS_CONFIG
end

-- 啟用/禁用工具
function DevTools.setEnabled(enabled)
	TOOLS_CONFIG.enabled = enabled
	print("🔧 DevTools:", enabled and "Enabled" or "Disabled")
end

return DevTools
