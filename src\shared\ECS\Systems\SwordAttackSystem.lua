--[[
	劍擊攻擊系統
	處理玩家和寵物的劍擊攻擊邏輯
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(script.Parent.Parent.Components)

local function SwordAttackSystem(world, deltaTime)
	-- 查詢所有正在揮劍的實體
	for entityId, swordSwing, position, damage in world:query(Components.SwordSwing, Components.Position, Components.Damage) do
		if swordSwing.isSwinging then
			local currentTime = tick()
			local swingProgress = (currentTime - swordSwing.swingStartTime) / swordSwing.swingDuration
			
			-- 檢查揮劍是否完成
			if swingProgress >= 1.0 then
				-- 結束揮劍
				world:insert(entityId, swordSwing:patch({
					isSwinging = false,
					swingStartTime = 0,
				}))
			else
				-- 在揮劍過程中檢測命中
				local swingPosition = position.position
				local swingRange = swordSwing.range
				local swingDamage = swordSwing.damage
				
				-- 查詢範圍內的目標
				for targetId, targetPosition, targetHealth in world:query(Components.Position, Components.Health) do
					if targetId ~= entityId then -- 不攻擊自己
						local distance = (targetPosition.position - swingPosition).Magnitude
						
						if distance <= swingRange then
							-- 檢查是否為有效目標
							local isValidTarget = false
							
							-- 如果攻擊者是玩家，可以攻擊怪物
							if world:get(entityId, Components.Player) and world:get(targetId, Components.Monster) then
								isValidTarget = true
							end
							
							-- 如果攻擊者是寵物，可以攻擊怪物
							if world:get(entityId, Components.Pet) and world:get(targetId, Components.Monster) then
								isValidTarget = true
							end
							
							-- 如果攻擊者是怪物，可以攻擊玩家和寵物
							if world:get(entityId, Components.Monster) then
								if world:get(targetId, Components.Player) or world:get(targetId, Components.Pet) then
									isValidTarget = true
								end
							end
							
							if isValidTarget then
								-- 計算傷害
								local targetDamage = world:get(targetId, Components.Damage)
								local finalDamage = math.max(1, swingDamage - (targetDamage and targetDamage.defense or 0))
								
								-- 檢查暴擊
								if damage.criticalChance > 0 and math.random() < damage.criticalChance then
									finalDamage = finalDamage * damage.criticalMultiplier
									print("💥 Critical hit! Damage:", finalDamage)
								end
								
								-- 造成傷害
								local newHealth = math.max(0, targetHealth.current - finalDamage)
								world:insert(targetId, targetHealth:patch({
									current = newHealth
								}))
								
								print("⚔️ Sword attack hit! Damage:", finalDamage, "Remaining health:", newHealth)
								
								-- 如果目標死亡，添加死亡標記
								if newHealth <= 0 then
									-- 可以在這裡添加死亡處理邏輯
									print("💀 Target defeated!")
								end
							end
						end
					end
				end
			end
		end
	end
end

return SwordAttackSystem
