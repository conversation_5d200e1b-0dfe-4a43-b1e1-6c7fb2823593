--[[
	PetController - 寵物控制器
	處理寵物召喚、收回和圖鑑顯示
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local PetConfig = require(game:GetService("ReplicatedStorage").Shared.PetConfig)

local PetController = Knit.CreateController({
	Name = "PetController",
})

-- 私有變量
local player = Players.LocalPlayer
local currentPet = nil
local playerPets = {}
local petDexData = {}

function PetController:KnitStart()
	print("🐾 PetController started")

	-- 獲取服務引用
	self.PetService = Knit.GetService("PetService")

	-- 請求寵物圖鑑數據
	self.PetService.GetPetDex:Fire()

	-- 監聽服務端事件
	self:_connectToServices()
end

function PetController:KnitInit()
	-- 初始化控制器依賴
end



-- 連接到服務端服務
function PetController:_connectToServices()
	-- 監聽寵物圖鑑數據
	self.PetService.GetPetDex:Connect(function(pets, dexData)
		playerPets = pets
		petDexData = dexData
		print("🐾 Received pet data:", pets)
	end)
	
	-- 監聽寵物召喚事件
	self.PetService.PetSummoned:Connect(function(petId)
		currentPet = petId
		print("🐾 Pet summoned:", petId)
		self:_showPetSummonedMessage(petId)
	end)
	
	-- 監聽寵物收回事件
	self.PetService.PetRecalled:Connect(function()
		local previousPet = currentPet
		currentPet = nil
		print("🐾 Pet recalled:", previousPet)
		self:_showPetRecalledMessage(previousPet)
	end)
end



-- 召喚指定寵物
function PetController:SummonPet(petId)
	if playerPets[petId] then
		self.PetService.SummonPet:Fire(petId)
	else
		self:_showMessage("你還沒有這隻寵物！", Color3.fromRGB(255, 100, 100))
	end
end

-- 收回寵物
function PetController:RecallPet()
	if currentPet then
		self.PetService.RecallPet:Fire()
	end
end

-- 獲取當前寵物
function PetController:GetCurrentPet()
	return currentPet
end

-- 獲取玩家寵物數據
function PetController:GetPlayerPets()
	return playerPets
end

-- 顯示寵物召喚消息
function PetController:_showPetSummonedMessage(petId)
	local config = PetConfig.PETS[petId]
	if config then
		print("✨ " .. config.name .. " 已召喚！")
	end
end

-- 顯示寵物收回消息
function PetController:_showPetRecalledMessage(petId)
	local config = PetConfig.PETS[petId]
	if config then
		print("💤 " .. config.name .. " 已收回！")
	end
end

return PetController
