--[[
	WeaponService.lua - 武器服務
	處理武器裝備、升級、特效觸發
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

-- 資料模組
local WeaponDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.WeaponDatabase)

-- ECS 組件
local DamageComponent = require(game:GetService("ReplicatedStorage").ECS.Components.DamageComponent)
local SwordSwingComponent = require(game:GetService("ReplicatedStorage").ECS.Components.SwordSwingComponent)

local WeaponService = Knit.CreateService({
	Name = "WeaponService",
	Client = {
		-- 客戶端可調用的方法
		EquipWeapon = Knit.CreateSignal(),
		UnequipWeapon = Knit.CreateSignal(),
		UpgradeWeapon = Knit.CreateSignal(),
		GetWeaponData = Knit.CreateSignal(),
		
		-- 客戶端事件
		WeaponEquipped = Knit.CreateSignal(),
		WeaponUnequipped = Knit.CreateSignal(),
		WeaponUpgraded = Knit.CreateSignal(),
		WeaponEffectTriggered = Knit.CreateSignal(),
	},
})

function WeaponService:KnitStart()
	print("⚔️ WeaponService started")
	
	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found!")
		return
	end
end

function WeaponService:KnitInit()
	-- 獲取其他服務
	self.PlayerService = Knit.GetService("PlayerService")
end

function WeaponService:KnitStart()
	print("⚔️ WeaponService started")

	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found!")
		return
	end

	-- 連接客戶端信號 (在 KnitStart 中確保信號已轉換)
	self.Client.EquipWeapon:Connect(function(player, weaponId)
		self:_equipWeapon(player, weaponId)
	end)

	self.Client.UnequipWeapon:Connect(function(player)
		self:_unequipWeapon(player)
	end)

	self.Client.UpgradeWeapon:Connect(function(player, weaponId)
		self:_upgradeWeapon(player, weaponId)
	end)

	self.Client.GetWeaponData:Connect(function(player)
		self:_sendWeaponData(player)
	end)
end



-- 裝備武器
function WeaponService:_equipWeapon(player, weaponId)
	local profile = self.PlayerService:GetPlayerProfile(player)
	if not profile then
		warn("❌ Player profile not found:", player.Name)
		return
	end
	
	-- 檢查玩家是否擁有此武器
	if not profile.Data.ownedWeapons[weaponId] then
		warn("Player", player.Name, "doesn't own weapon:", weaponId)
		return
	end
	
	-- 獲取武器配置
	local weaponConfig = WeaponDatabase.getWeapon(weaponId)
	if not weaponConfig then
		warn("Weapon config not found:", weaponId)
		return
	end
	
	-- 檢查等級需求
	if profile.Data.level < weaponConfig.requirements.level then
		warn("Player level too low for weapon:", weaponId)
		return
	end
	
	-- 更新玩家檔案
	profile.Data.equippedWeapon = weaponId
	
	-- 更新玩家實體的傷害組件
	local playerEntityId = self.PlayerService:GetPlayerEntityId(player)
	if playerEntityId then
		self:_updatePlayerWeaponStats(playerEntityId, weaponId, profile.Data.ownedWeapons[weaponId])
	end
	
	-- 通知客戶端
	self.Client.WeaponEquipped:Fire(player, weaponId, weaponConfig)
	
	print("⚔️ Weapon equipped:", weaponId, "for", player.Name)
end

-- 卸下武器
function WeaponService:_unequipWeapon(player)
	local profile = self.PlayerService:GetPlayerProfile(player)
	if not profile then return end
	
	local previousWeapon = profile.Data.equippedWeapon
	profile.Data.equippedWeapon = nil
	
	-- 重置玩家實體的傷害組件為基礎值
	local playerEntityId = self.PlayerService:GetPlayerEntityId(player)
	if playerEntityId then
		self:_resetPlayerWeaponStats(playerEntityId)
	end
	
	-- 通知客戶端
	self.Client.WeaponUnequipped:Fire(player, previousWeapon)
	
	print("⚔️ Weapon unequipped for", player.Name)
end

-- 升級武器
function WeaponService:_upgradeWeapon(player, weaponId)
	local profile = self.PlayerService:GetPlayerProfile(player)
	if not profile then return end
	
	local weaponData = profile.Data.ownedWeapons[weaponId]
	if not weaponData then
		warn("Player doesn't own weapon:", weaponId)
		return
	end
	
	-- 計算升級費用
	local upgradeCost = self:_calculateUpgradeCost(weaponId, weaponData.level)
	
	-- 檢查金幣是否足夠
	if profile.Data.coins < upgradeCost then
		warn("Insufficient coins for weapon upgrade")
		return
	end
	
	-- 扣除金幣
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	if PlayerProfile.spendCoins(player, upgradeCost) then
		-- 升級武器
		weaponData.level = weaponData.level + 1
		weaponData.experience = 0
		
		-- 如果是當前裝備的武器，更新屬性
		if profile.Data.equippedWeapon == weaponId then
			local playerEntityId = self.PlayerService:GetPlayerEntityId(player)
			if playerEntityId then
				self:_updatePlayerWeaponStats(playerEntityId, weaponId, weaponData)
			end
		end
		
		-- 通知客戶端
		self.Client.WeaponUpgraded:Fire(player, weaponId, weaponData.level)
		
		print("⚔️ Weapon upgraded:", weaponId, "to level", weaponData.level, "for", player.Name)
	end
end

-- 更新玩家武器屬性
function WeaponService:_updatePlayerWeaponStats(playerEntityId, weaponId, weaponData)
	local weaponConfig = WeaponDatabase.getWeapon(weaponId)
	if not weaponConfig then return end
	
	-- 計算武器等級加成
	local levelMultiplier = 1 + (weaponData.level - 1) * 0.1 -- 每級增加10%
	
	local damageComponent = self.world:get(playerEntityId, DamageComponent)
	if damageComponent then
		self.world:insert(playerEntityId, damageComponent:patch({
			attack = damageComponent.attack + math.floor(weaponConfig.stats.damage * levelMultiplier),
			criticalChance = math.min(0.5, damageComponent.criticalChance + weaponConfig.stats.criticalChance),
			criticalMultiplier = math.max(damageComponent.criticalMultiplier, weaponConfig.stats.criticalMultiplier),
			elementalType = weaponConfig.stats.elementalDamage and weaponConfig.stats.elementalDamage.type or "None",
			elementalDamage = weaponConfig.stats.elementalDamage and weaponConfig.stats.elementalDamage.damage or 0,
		}))
	end
	
	-- 更新劍擊組件
	local swordSwingComponent = self.world:get(playerEntityId, SwordSwingComponent)
	if swordSwingComponent then
		self.world:insert(playerEntityId, swordSwingComponent:patch({
			range = weaponConfig.stats.range,
			swingDuration = 1.0 / weaponConfig.stats.attackSpeed,
			weaponId = weaponId,
		}))
	else
		-- 創建劍擊組件
		self.world:insert(playerEntityId, SwordSwingComponent({
			isSwinging = false,
			swingDuration = 1.0 / weaponConfig.stats.attackSpeed,
			swingStartTime = 0,
			range = weaponConfig.stats.range,
			damage = weaponConfig.stats.damage,
			weaponId = weaponId,
			hasHit = {},
		}))
	end
end

-- 重置玩家武器屬性
function WeaponService:_resetPlayerWeaponStats(playerEntityId)
	local damageComponent = self.world:get(playerEntityId, DamageComponent)
	if damageComponent then
		-- 重置為基礎屬性
		self.world:insert(playerEntityId, damageComponent:patch({
			attack = 20, -- 基礎攻擊力
			criticalChance = 0.1,
			criticalMultiplier = 2.0,
			elementalType = "None",
			elementalDamage = 0,
		}))
	end
	
	-- 移除劍擊組件或重置為基礎值
	local swordSwingComponent = self.world:get(playerEntityId, SwordSwingComponent)
	if swordSwingComponent then
		self.world:insert(playerEntityId, swordSwingComponent:patch({
			range = 8, -- 基礎範圍
			swingDuration = 0.8, -- 基礎攻擊速度
			weaponId = "",
		}))
	end
end

-- 計算升級費用
function WeaponService:_calculateUpgradeCost(weaponId, currentLevel)
	local weaponConfig = WeaponDatabase.getWeapon(weaponId)
	if not weaponConfig then return 999999 end
	
	-- 基礎費用根據稀有度決定
	local baseCost = 100
	local rarityMultiplier = {
		Common = 1,
		Uncommon = 2,
		Rare = 4,
		Epic = 8,
		Legendary = 16,
	}
	
	local multiplier = rarityMultiplier[weaponConfig.rarity] or 1
	return math.floor(baseCost * multiplier * (currentLevel ^ 1.5))
end

-- 發送武器資料
function WeaponService:_sendWeaponData(player)
	local profile = self.PlayerService:GetPlayerProfile(player)
	if profile then
		self.Client.GetWeaponData:Fire(player, profile.Data.ownedWeapons, profile.Data.equippedWeapon)
	end
end

-- 觸發武器特效
function WeaponService:TriggerWeaponEffect(player, weaponId, effectType)
	local weaponConfig = WeaponDatabase.getWeapon(weaponId)
	if weaponConfig and weaponConfig.effects then
		-- 通知客戶端播放特效
		self.Client.WeaponEffectTriggered:Fire(player, weaponId, effectType, weaponConfig.effects)
	end
end

-- 添加武器到玩家背包
function WeaponService:AddWeaponToPlayer(player, weaponId, weaponData)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	return PlayerProfile.addWeapon(player, weaponId, weaponData)
end



return WeaponService
