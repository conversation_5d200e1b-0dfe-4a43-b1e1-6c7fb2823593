--[[
	ClientInit.lua - 客戶端初始化腳本
	Knit 控制器啟動點，整合 Fusion UI 系統
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)

-- 載入所有控制器
local Controllers = script.Parent.Controllers
for _, controller in pairs(Controllers:GetChildren()) do
	if controller:IsA("ModuleScript") then
		require(controller)
	end
end

-- 啟動 Knit
Knit.Start():andThen(function()
	print("🎮 Client started successfully!")
	print("🎨 Fusion UI system ready")
	
	-- 初始化 UI 系統
	local UIController = Knit.GetController("UIController")
	if UIController then
		UIController:InitializeUI()
	end
	
	-- 初始化本地化系統
	local i18n = require(game:GetService("ReplicatedStorage").Shared.i18n)
	i18n.init("zh-TW") -- 預設繁體中文
	
	print("🌐 Localization system initialized")
	
end):catch(function(err)
	warn("❌ Client startup failed:", err)
end)
