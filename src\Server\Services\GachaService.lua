--[[
	GachaService.lua - 抽卡服務
	處理抽卡邏輯、機率選擇、金幣扣除
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

-- 資料模組
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.PetDatabase)
local WeaponDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.WeaponDatabase)

local GachaService = Knit.CreateService({
	Name = "GachaService",
	Client = {
		-- 客戶端可調用的方法
		SinglePull = Knit.CreateSignal(),
		TenPull = Knit.CreateSignal(),
		GetGachaData = Knit.CreateSignal(),
		
		-- 客戶端事件
		GachaResult = Knit.CreateSignal(),
		GachaAnimation = Knit.CreateSignal(),
		InsufficientFunds = Knit.CreateSignal(),
		GachaDataUpdated = Knit.CreateSignal(),
	},
})

-- 抽卡配置
local GACHA_CONFIG = {
	singlePullCost = 50,  -- 降低單抽費用
	tenPullCost = 450,    -- 降低十連抽費用 (10% 折扣)
	guaranteedRareAfter = 10, -- 10抽保底稀有
	guaranteedEpicAfter = 50, -- 50抽保底傳說
	guaranteedLegendaryAfter = 100, -- 100抽保底神話
}

function GachaService:KnitStart()
	print("🎰 GachaService started")

	-- 連接客戶端信號
	self.Client.SinglePull:Connect(function(player, poolType)
		self:_performSinglePull(player, poolType or "pet")
	end)

	self.Client.TenPull:Connect(function(player, poolType)
		print("🎰 TenPull signal received from:", player.Name, "Pool:", poolType)
		self:_performTenPull(player, poolType or "pet")
	end)

	self.Client.GetGachaData:Connect(function(player)
		print("🎰 GetGachaData request from:", player.Name)
		self:_sendGachaData(player)
	end)
end

function GachaService:KnitInit()
	-- 獲取其他服務
	self.PlayerService = Knit.GetService("PlayerService")
	self.PetService = Knit.GetService("PetService")
	self.WeaponService = Knit.GetService("WeaponService")
end



-- 執行單抽
function GachaService:_performSinglePull(player, poolType)
	-- 直接從 ProfileService 獲取檔案
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	if not profile then return end
	
	-- 檢查金幣
	if profile.Data.coins < GACHA_CONFIG.singlePullCost then
		self.Client.InsufficientFunds:Fire(player, GACHA_CONFIG.singlePullCost, profile.Data.coins)
		return
	end
	
	-- 扣除金幣
	if not PlayerProfile.spendCoins(player, GACHA_CONFIG.singlePullCost) then
		return
	end
	
	-- 執行抽卡
	local result = self:_performGachaPull(player, poolType)
	
	-- 更新統計
	profile.Data.statistics.gachaRolls = profile.Data.statistics.gachaRolls + 1
	
	-- 記錄抽卡歷史
	table.insert(profile.Data.gachaHistory, {
		type = poolType,
		result = result,
		timestamp = os.time(),
		cost = GACHA_CONFIG.singlePullCost,
	})
	
	-- 保持歷史記錄在100條以內
	if #profile.Data.gachaHistory > 100 then
		table.remove(profile.Data.gachaHistory, 1)
	end
	
	-- 通知客戶端
	self.Client.GachaResult:Fire(player, {result}, poolType, false)
	
	print("🎰 Single pull completed for", player.Name, "Result:", result.id, result.rarity)
end

-- 執行十連抽
function GachaService:_performTenPull(player, poolType)
	print("🎰 TenPull requested by:", player.Name, "Pool:", poolType)

	-- 直接從 ProfileService 獲取檔案
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	if not profile then
		warn("❌ Player profile not found:", player.Name)
		return
	end
	
	-- 檢查金幣
	if profile.Data.coins < GACHA_CONFIG.tenPullCost then
		self.Client.InsufficientFunds:Fire(player, GACHA_CONFIG.tenPullCost, profile.Data.coins)
		return
	end
	
	-- 扣除金幣
	if not PlayerProfile.spendCoins(player, GACHA_CONFIG.tenPullCost) then
		return
	end
	
	-- 執行10次抽卡
	local results = {}
	for i = 1, 10 do
		local result = self:_performGachaPull(player, poolType, i == 10) -- 最後一抽有保底

		if result then
			table.insert(results, result)

			-- 記錄抽卡歷史
			table.insert(profile.Data.gachaHistory, {
				type = poolType,
				result = result,
				timestamp = os.time(),
				cost = GACHA_CONFIG.tenPullCost / 10,
			})
		else
			warn("❌ Failed to perform gacha pull", i, "for", player.Name)
		end
	end
	
	-- 更新統計
	profile.Data.statistics.gachaRolls = profile.Data.statistics.gachaRolls + 10
	
	-- 保持歷史記錄在100條以內
	while #profile.Data.gachaHistory > 100 do
		table.remove(profile.Data.gachaHistory, 1)
	end
	
	-- 通知客戶端
	self.Client.GachaResult:Fire(player, results, poolType, true)
	
	print("🎰 Ten pull completed for", player.Name, "Results:", #results)
end

-- 執行單次抽卡
function GachaService:_performGachaPull(player, poolType, isGuaranteed)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	if not profile then return nil end
	
	-- 確定稀有度
	local rarity = self:_determineRarity(player, isGuaranteed)
	
	-- 根據池子類型選擇物品
	local item
	if poolType == "pet" then
		item = self:_selectRandomPet(rarity)
	elseif poolType == "weapon" then
		item = self:_selectRandomWeapon(rarity)
	else
		-- 混合池
		if math.random() < 0.7 then
			item = self:_selectRandomPet(rarity)
		else
			item = self:_selectRandomWeapon(rarity)
		end
	end
	
	if not item then
		warn("❌ Failed to select gacha item")
		return nil
	end
	
	-- 處理獲得的物品
	local isNew = false
	if item.type == "pet" then
		isNew = not profile.Data.ownedPets[item.id]
		self:_addPetToPlayer(player, item.id, item.rarity)
	elseif item.type == "weapon" then
		isNew = not profile.Data.ownedWeapons[item.id]
		self:_addWeaponToPlayer(player, item.id, item.rarity)
	end
	
	-- 更新保底計數
	self:_updatePityCounters(player, rarity)
	
	return {
		id = item.id,
		name = item.name,
		rarity = rarity,
		type = item.type,
		isNew = isNew,
		description = item.description,
	}
end

-- 確定稀有度
function GachaService:_determineRarity(player, isGuaranteed)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	if not profile then return "Common" end
	
	local pity = profile.Data.gachaPity
	
	-- 檢查保底
	if pity.legendary >= GACHA_CONFIG.guaranteedLegendaryAfter then
		return "Legendary"
	elseif pity.epic >= GACHA_CONFIG.guaranteedEpicAfter then
		return "Epic"
	elseif pity.rare >= GACHA_CONFIG.guaranteedRareAfter or isGuaranteed then
		return "Rare"
	end
	
	-- 正常機率抽取
	local random = math.random()
	local cumulativeProbability = 0
	
	for rarity, data in pairs(PetDatabase.Rarities) do
		cumulativeProbability = cumulativeProbability + data.probability
		if random <= cumulativeProbability then
			return rarity
		end
	end
	
	return "Common" -- 保底
end

-- 選擇隨機寵物
function GachaService:_selectRandomPet(rarity)
	local pets = PetDatabase.getPetsByRarity(rarity)
	local petList = {}
	
	for petId, petData in pairs(pets) do
		table.insert(petList, {
			id = petId,
			name = petData.name,
			type = "pet",
			description = petData.description,
		})
	end
	
	if #petList > 0 then
		return petList[math.random(1, #petList)]
	end
	
	return nil
end

-- 選擇隨機武器
function GachaService:_selectRandomWeapon(rarity)
	local weapons = WeaponDatabase.getWeaponsByRarity(rarity)
	local weaponList = {}
	
	for weaponId, weaponData in pairs(weapons) do
		table.insert(weaponList, {
			id = weaponId,
			name = weaponData.name,
			type = "weapon",
			description = weaponData.description,
		})
	end
	
	if #weaponList > 0 then
		return weaponList[math.random(1, #weaponList)]
	end
	
	return nil
end

-- 添加寵物到玩家
function GachaService:_addPetToPlayer(player, petId, rarity)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	local petData = {
		level = 1,
		experience = 0,
		rarity = rarity,
		obtainedTime = os.time(),
	}
	
	PlayerProfile.addPet(player, petId, petData)
end

-- 添加武器到玩家
function GachaService:_addWeaponToPlayer(player, weaponId, rarity)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	local weaponData = {
		level = 1,
		experience = 0,
		rarity = rarity,
		obtainedTime = os.time(),
	}
	
	PlayerProfile.addWeapon(player, weaponId, weaponData)
end

-- 更新保底計數
function GachaService:_updatePityCounters(player, obtainedRarity)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	if not profile then return end
	
	local pity = profile.Data.gachaPity
	
	if obtainedRarity == "Legendary" then
		pity.legendary = 0
		pity.epic = 0
		pity.rare = 0
	elseif obtainedRarity == "Epic" then
		pity.epic = 0
		pity.rare = 0
		pity.legendary = pity.legendary + 1
	elseif obtainedRarity == "Rare" then
		pity.rare = 0
		pity.epic = pity.epic + 1
		pity.legendary = pity.legendary + 1
	else
		pity.rare = pity.rare + 1
		pity.epic = pity.epic + 1
		pity.legendary = pity.legendary + 1
	end
end

-- 發送抽卡資料
function GachaService:_sendGachaData(player)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	if profile then
		print("🎰 Sending gacha data to:", player.Name)
		self.Client.GachaDataUpdated:Fire(player, {
			history = profile.Data.gachaHistory,
			pity = profile.Data.gachaPity,
			costs = GACHA_CONFIG,
		})
		print("🎰 Gacha data sent successfully")
	else
		warn("❌ No profile found for player:", player.Name)
	end
end



return GachaService
