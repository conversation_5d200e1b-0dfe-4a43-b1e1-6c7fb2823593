--[[
	PetController.lua - 寵物控制器
	前端控制寵物圖鑑、UI 顯示
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)

-- Fusion 組件
local New = Fusion.New
local Children = Fusion.Children
local OnEvent = Fusion.OnEvent
local Value = Fusion.Value
local Computed = Fusion.Computed
local cleanup = Fusion.cleanup

-- 資料模組
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.PetDatabase)
local i18n = require(game:GetService("ReplicatedStorage").Shared.i18n)

local PetController = Knit.CreateController({
	Name = "PetController",
})

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- 狀態
local ownedPets = Value({})
local petDex = Value({})
local currentPet = Value(nil)
local selectedPet = Value(nil)
local isPetUIVisible = Value(false)

-- UI 引用
local petUI = nil

function PetController:KnitStart()
	print("🐾 PetController started")
	
	-- 獲取服務
	self.PetService = Knit.GetService("PetService")
	self.UIController = Knit.GetController("UIController")
	
	-- 監聽服務端事件
	self:_connectToServices()
	
	-- 等待 Knit 完全啟動後請求寵物數據
	Knit.OnStart():andThen(function()
		self.PetService.GetPetDex:Fire()
	end)
	
	-- 創建寵物 UI
	self:_createPetUI()
end

function PetController:KnitInit()
	-- 初始化時不需要其他控制器
end

-- 連接到服務端服務
function PetController:_connectToServices()
	-- 監聽寵物數據更新事件
	self.PetService.PetDataUpdated:Connect(function(pets, dexData)
		ownedPets:set(pets or {})
		petDex:set(dexData or {})
		print("🐾 Received pet data:", pets)
	end)
	
	-- 監聽寵物召喚事件
	self.PetService.PetSummoned:Connect(function(petId)
		currentPet:set(petId)
		print("🐾 Pet summoned:", petId)
		
		local petConfig = PetDatabase.getPet(petId)
		if petConfig and self.UIController then
			self.UIController:ShowNotification(
				i18n.t("pets.summon_success", {petName = petConfig.name}),
				3,
				Color3.fromRGB(0, 255, 0)
			)
		end
	end)
	
	-- 監聽寵物收回事件
	self.PetService.PetRecalled:Connect(function()
		local previousPet = currentPet:get()
		currentPet:set(nil)
		print("🐾 Pet recalled:", previousPet)
		
		if self.UIController then
			self.UIController:ShowNotification(
				i18n.t("pets.recall_success"),
				2,
				Color3.fromRGB(255, 255, 0)
			)
		end
	end)
	
	-- 監聽寵物升級事件
	self.PetService.PetLevelUp:Connect(function(petId, newLevel)
		print("🐾 Pet leveled up:", petId, "to level", newLevel)
		
		local petConfig = PetDatabase.getPet(petId)
		if petConfig and self.UIController then
			self.UIController:ShowNotification(
				i18n.t("pets.level_up", {petName = petConfig.name}),
				3,
				Color3.fromRGB(255, 215, 0)
			)
		end
	end)
end

-- 創建寵物 UI
function PetController:_createPetUI()
	petUI = New "ScreenGui" {
		Name = "PetUI",
		Parent = playerGui,
		Enabled = Computed(function()
			return isPetUIVisible:get()
		end, cleanup),
		ResetOnSpawn = false,
		ZIndexBehavior = Enum.ZIndexBehavior.Sibling,

		[Children] = {
			-- 背景遮罩
			New "TextButton" {
				Name = "Background",
				Size = UDim2.fromScale(1, 1),
				BackgroundColor3 = Color3.fromRGB(0, 0, 0),
				BackgroundTransparency = 0.5,
				BorderSizePixel = 0,
				Text = "",

				[OnEvent "Activated"] = function()
					self:HidePetUI()
				end,
			},

			-- 主要面板
			New "Frame" {
				Name = "MainPanel",
				Size = UDim2.fromScale(0.8, 0.8),
				Position = UDim2.fromScale(0.1, 0.1),
				BackgroundColor3 = Color3.fromRGB(50, 50, 50),
				BorderSizePixel = 0,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 12),
					},

					-- 標題
					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(1, 0, 0, 50),
						Position = UDim2.fromScale(0, 0),
						BackgroundTransparency = 1,
						Text = i18n.t("pets.title"),
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					-- 關閉按鈕
					New "TextButton" {
						Name = "CloseButton",
						Size = UDim2.fromOffset(40, 40),
						Position = UDim2.new(1, -50, 0, 5),
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						BorderSizePixel = 0,
						Text = "✕",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[OnEvent "Activated"] = function()
							self:HidePetUI()
						end,
						
						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 8),
							}
						}
					},
					
					-- 寵物列表
					self:_createPetList(),
					
					-- 寵物詳情
					self:_createPetDetails(),
					
					-- 操作按鈕
					self:_createActionButtons(),
				}
			}
		}
	}
end

-- 創建寵物列表
function PetController:_createPetList()
	return New "ScrollingFrame" {
		Name = "PetList",
		Size = UDim2.new(0.4, -10, 1, -120),
		Position = UDim2.new(0, 10, 0, 60),
		BackgroundColor3 = Color3.fromRGB(40, 40, 40),
		BorderSizePixel = 0,
		ScrollBarThickness = 8,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},
			
			New "UIListLayout" {
				SortOrder = Enum.SortOrder.LayoutOrder,
				Padding = UDim.new(0, 5),
			},
			
			New "UIPadding" {
				PaddingTop = UDim.new(0, 10),
				PaddingBottom = UDim.new(0, 10),
				PaddingLeft = UDim.new(0, 10),
				PaddingRight = UDim.new(0, 10),
			},
			
			[Children] = Computed(function()
				local children = {}
				local pets = ownedPets:get()
				local dex = petDex:get()
				
				-- 顯示所有已解鎖的寵物
				for petId, isUnlocked in pairs(dex) do
					if isUnlocked then
						local petConfig = PetDatabase.getPet(petId)
						local petData = pets[petId]
						
						if petConfig then
							children[petId] = self:_createPetListItem(petId, petConfig, petData)
						end
					end
				end
				
				return children
			end, cleanup)
		}
	}
end

-- 創建寵物列表項目
function PetController:_createPetListItem(petId, petConfig, petData)
	local isOwned = petData ~= nil
	local isSelected = Computed(function()
		return selectedPet:get() == petId
	end, cleanup)
	
	return New "TextButton" {
		Name = "PetItem_" .. petId,
		Size = UDim2.new(1, 0, 0, 80),
		BackgroundColor3 = Computed(function()
			if isSelected:get() then
				return Color3.fromRGB(70, 130, 180)
			elseif isOwned then
				return Color3.fromRGB(60, 60, 60)
			else
				return Color3.fromRGB(30, 30, 30)
			end
		end, cleanup),
		BorderSizePixel = 0,
		Text = "",
		
		[OnEvent "Activated"] = function()
			selectedPet:set(petId)
		end,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},
			
			-- 寵物圖標
			New "Frame" {
				Name = "Icon",
				Size = UDim2.fromOffset(60, 60),
				Position = UDim2.new(0, 10, 0.5, -30),
				BackgroundColor3 = petConfig.appearance.primaryColor,
				BorderSizePixel = 0,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 8),
					}
				}
			},
			
			-- 寵物名稱
			New "TextLabel" {
				Name = "Name",
				Size = UDim2.new(1, -80, 0, 25),
				Position = UDim2.new(0, 80, 0, 10),
				BackgroundTransparency = 1,
				Text = petConfig.name,
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},
			
			-- 等級/狀態
			New "TextLabel" {
				Name = "Level",
				Size = UDim2.new(1, -80, 0, 20),
				Position = UDim2.new(0, 80, 0, 35),
				BackgroundTransparency = 1,
				Text = isOwned and (i18n.t("ui.labels.level") .. " " .. (petData.level or 1)) or "未擁有",
				TextColor3 = isOwned and Color3.fromRGB(200, 200, 200) or Color3.fromRGB(150, 150, 150),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
			},
			
			-- 稀有度星級
			New "TextLabel" {
				Name = "Rarity",
				Size = UDim2.new(1, -80, 0, 20),
				Position = UDim2.new(0, 80, 0, 55),
				BackgroundTransparency = 1,
				Text = string.rep("⭐", PetDatabase.getRarity(petConfig.rarity).stars),
				TextColor3 = PetDatabase.getRarity(petConfig.rarity).color,
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
			},
		}
	}
end

-- 創建寵物詳情
function PetController:_createPetDetails()
	return New "Frame" {
		Name = "PetDetails",
		Size = UDim2.new(0.6, -10, 1, -120),
		Position = UDim2.new(0.4, 10, 0, 60),
		BackgroundColor3 = Color3.fromRGB(40, 40, 40),
		BorderSizePixel = 0,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},
			
			[Children] = Computed(function()
				local petId = selectedPet:get()
				if not petId then
					return {
						New "TextLabel" {
							Size = UDim2.fromScale(1, 1),
							BackgroundTransparency = 1,
							Text = "選擇一隻寵物查看詳情",
							TextColor3 = Color3.fromRGB(150, 150, 150),
							TextScaled = true,
							Font = Enum.Font.Gotham,
						}
					}
				end
				
				local petConfig = PetDatabase.getPet(petId)
				local petData = ownedPets:get()[petId]
				
				if not petConfig then return {} end
				
				return {
					-- 寵物名稱
					New "TextLabel" {
						Name = "DetailName",
						Size = UDim2.new(1, -20, 0, 40),
						Position = UDim2.new(0, 10, 0, 10),
						BackgroundTransparency = 1,
						Text = petConfig.name,
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					-- 描述
					New "TextLabel" {
						Name = "Description",
						Size = UDim2.new(1, -20, 0, 60),
						Position = UDim2.new(0, 10, 0, 60),
						BackgroundTransparency = 1,
						Text = petConfig.description,
						TextColor3 = Color3.fromRGB(200, 200, 200),
						TextScaled = true,
						Font = Enum.Font.Gotham,
						TextWrapped = true,
					},
					
					-- 屬性顯示
					self:_createStatsDisplay(petConfig, petData),
				}
			end, cleanup)
		}
	}
end

-- 創建屬性顯示
function PetController:_createStatsDisplay(petConfig, petData)
	local level = petData and petData.level or 1
	local stats = PetDatabase.calculateStats(petConfig.id, level)
	
	return New "Frame" {
		Name = "StatsDisplay",
		Size = UDim2.new(1, -20, 0, 200),
		Position = UDim2.new(0, 10, 0, 130),
		BackgroundTransparency = 1,
		
		[Children] = {
			New "UIListLayout" {
				SortOrder = Enum.SortOrder.LayoutOrder,
				Padding = UDim.new(0, 5),
			},
			
			-- 血量
			self:_createStatBar(i18n.t("ui.labels.health"), stats.health, Color3.fromRGB(255, 100, 100)),
			-- 攻擊力
			self:_createStatBar(i18n.t("ui.labels.attack"), stats.attack, Color3.fromRGB(255, 150, 100)),
			-- 防禦力
			self:_createStatBar(i18n.t("ui.labels.defense"), stats.defense, Color3.fromRGB(100, 150, 255)),
			-- 速度
			self:_createStatBar(i18n.t("ui.labels.speed"), stats.speed, Color3.fromRGB(100, 255, 100)),
		}
	}
end

-- 創建屬性條
function PetController:_createStatBar(statName, value, color)
	return New "Frame" {
		Size = UDim2.new(1, 0, 0, 30),
		BackgroundTransparency = 1,
		
		[Children] = {
			New "TextLabel" {
				Size = UDim2.new(0, 80, 1, 0),
				BackgroundTransparency = 1,
				Text = statName .. ":",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
			},
			
			New "Frame" {
				Size = UDim2.new(1, -100, 0, 20),
				Position = UDim2.new(0, 90, 0.5, -10),
				BackgroundColor3 = Color3.fromRGB(30, 30, 30),
				BorderSizePixel = 0,
				
				[Children] = {
					New "Frame" {
						Size = UDim2.fromScale(math.min(value / 100, 1), 1),
						BackgroundColor3 = color,
						BorderSizePixel = 0,
					},
					
					New "TextLabel" {
						Size = UDim2.fromScale(1, 1),
						BackgroundTransparency = 1,
						Text = tostring(value),
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.Gotham,
					},
				}
			}
		}
	}
end

-- 創建操作按鈕
function PetController:_createActionButtons()
	return New "Frame" {
		Name = "ActionButtons",
		Size = UDim2.new(1, -20, 0, 50),
		Position = UDim2.new(0, 10, 1, -60),
		BackgroundTransparency = 1,
		
		[Children] = {
			-- 召喚按鈕
			New "TextButton" {
				Name = "SummonButton",
				Size = UDim2.new(0, 120, 1, 0),
				Position = UDim2.fromScale(0, 0),
				BackgroundColor3 = Color3.fromRGB(0, 150, 0),
				BorderSizePixel = 0,
				Text = i18n.t("ui.buttons.summon"),
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				
				[OnEvent "Activated"] = function()
					self:SummonSelectedPet()
				end,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 8),
					}
				}
			},
			
			-- 收回按鈕
			New "TextButton" {
				Name = "RecallButton",
				Size = UDim2.new(0, 120, 1, 0),
				Position = UDim2.new(0, 140, 0, 0),
				BackgroundColor3 = Color3.fromRGB(150, 150, 0),
				BorderSizePixel = 0,
				Text = i18n.t("ui.buttons.recall"),
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				
				[OnEvent "Activated"] = function()
					self:RecallPet()
				end,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 8),
					}
				}
			},
		}
	}
end

-- 顯示寵物 UI
function PetController:ShowPetUI()
	isPetUIVisible:set(true)
end

-- 隱藏寵物 UI
function PetController:HidePetUI()
	isPetUIVisible:set(false)
end

-- 召喚選中的寵物
function PetController:SummonSelectedPet()
	local petId = selectedPet:get()
	if petId then
		local pets = ownedPets:get()
		if pets[petId] then
			self.PetService.SummonPet:Fire(petId)
		else
			if self.UIController then
				self.UIController:ShowNotification(
					i18n.t("pets.not_owned"),
					2,
					Color3.fromRGB(255, 100, 100)
				)
			end
		end
	end
end

-- 收回寵物
function PetController:RecallPet()
	self.PetService.RecallPet:Fire()
end

return PetController
