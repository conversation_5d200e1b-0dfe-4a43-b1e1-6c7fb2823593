--[[
	FixPetService.lua - 修復 PetService 的 world 連接
]]

print("🔧 PetService 需要手動修復")
print("❗ 問題：PetService 沒有連接到 Matter World")
print("❗ 解決方案：在 KnitInit 中添加 world 連接")

print([[
修復代碼：

function PetService:KnitInit()
	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found in PetService!")
	else
		print("✅ PetService connected to Matter World")
	end
	
	-- 獲取其他服務
	self.PlayerService = Knit.GetService("PlayerService")
end
]])

print("🔧 請手動應用這個修復到 PetService.lua 的 KnitInit 函數")

return true
