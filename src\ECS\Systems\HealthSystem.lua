--[[
	HealthSystem.lua - 血量系統
	處理血量回復、死亡檢測等邏輯
]]

local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local PetComponent = require(script.Parent.Parent.Components.PetComponent)

local function HealthSystem(world, deltaTime)
	-- 查詢所有有血量的實體
	for entityId, health in world:query(HealthComponent) do
		local needsUpdate = false
		local newHealth = health.current
		
		-- 處理血量回復（只有在未死亡且未在戰鬥中時才回血）
		if not health.isDead and health.regeneration > 0 and health.current < health.maximum then
			local timeSinceLastDamage = tick() - health.lastDamageTime
			
			-- 脫離戰鬥5秒後開始回血
			if timeSinceLastDamage > 5 then
				newHealth = math.min(health.maximum, health.current + health.regeneration * deltaTime)
				needsUpdate = true
			end
		end
		
		-- 檢查死亡
		if health.current <= 0 and not health.isDead then
			-- 處理死亡邏輯
			local pet = world:get(entityId, PetComponent)
			
			if pet then
				-- 寵物死亡處理
				print("💀 Pet died:", pet.petId)
				
				-- 寵物死亡後暫時失活，等待復活或收回
				world:insert(entityId, pet:patch({
					isActive = false,
				}))
				
				-- 標記為死亡
				world:insert(entityId, health:patch({
					isDead = true,
				}))
				
			else
				-- 玩家或怪物死亡處理
				print("💀 Entity died:", entityId)
				
				-- 玩家死亡可能需要復活邏輯
				-- 怪物死亡可能需要給予獎勵
				
				-- 標記為死亡
				world:insert(entityId, health:patch({
					isDead = true,
				}))
			end
		end
		
		-- 更新血量
		if needsUpdate then
			world:insert(entityId, health:patch({
				current = newHealth
			}))
		end
		
		-- 同步血量到 UI（通過 RemoteEvent 發送給客戶端）
		-- 這裡可以發送血量更新事件
	end
end

return HealthSystem
