--[[
	TestRunner.lua - 測試運行器
	使用 TestEZ 框架運行所有測試
]]

local TestEZ = require(game:GetService("ReplicatedStorage").Packages.testez)

local TestRunner = {}

-- 測試配置
local TEST_CONFIG = {
	-- 測試超時時間（秒）
	timeout = 30,
	
	-- 是否顯示詳細輸出
	verbose = true,
	
	-- 測試環境設置
	environment = {
		-- 是否模擬網路延遲
		simulateNetworkDelay = false,
		
		-- 是否使用模擬數據
		useMockData = true,
		
		-- 測試用的玩家數據
		mockPlayerData = {
			level = 5,
			experience = 250,
			coins = 500,
			gems = 50,
			stats = {
				maxHealth = 150,
				currentHealth = 150,
				attack = 30,
				defense = 10,
				criticalChance = 0.15,
				criticalMultiplier = 2.2,
			},
			ownedPets = {
				Slime = {
					level = 3,
					experience = 80,
					rarity = "Common",
					obtainedTime = os.time() - 86400,
				},
				FireSpirit = {
					level = 2,
					experience = 45,
					rarity = "Uncommon",
					obtainedTime = os.time() - 3600,
				},
			},
			ownedWeapons = {
				WoodenSword = {
					level = 2,
					experience = 60,
					rarity = "Common",
					obtainedTime = os.time() - 86400,
				},
			},
		},
	},
}

-- 運行所有測試
function TestRunner.runAllTests()
	print("🧪 Starting test suite...")
	print("=" .. string.rep("=", 50))
	
	-- 設置測試環境
	TestRunner.setupTestEnvironment()
	
	-- 獲取測試模組
	local testModules = TestRunner.getTestModules()
	
	if #testModules == 0 then
		warn("⚠️ No test modules found!")
		return false
	end
	
	-- 運行測試
	local results = {}
	local totalTests = 0
	local passedTests = 0
	local failedTests = 0
	
	for _, testModule in ipairs(testModules) do
		print("\n🔍 Running tests in:", testModule.Name)
		
		local success, result = pcall(function()
			return TestEZ.run({testModule})
		end)
		
		if success then
			results[testModule.Name] = result
			
			-- 統計結果
			if result.results then
				for _, testResult in ipairs(result.results) do
					totalTests = totalTests + 1
					if testResult.status == "Success" then
						passedTests = passedTests + 1
					else
						failedTests = failedTests + 1
					end
				end
			end
		else
			warn("❌ Error running tests in", testModule.Name, ":", result)
			failedTests = failedTests + 1
		end
	end
	
	-- 顯示總結
	TestRunner.printSummary(totalTests, passedTests, failedTests, results)
	
	-- 清理測試環境
	TestRunner.cleanupTestEnvironment()
	
	return failedTests == 0
end

-- 運行特定測試
function TestRunner.runSpecificTest(testName)
	print("🧪 Running specific test:", testName)
	
	-- 設置測試環境
	TestRunner.setupTestEnvironment()
	
	-- 查找測試模組
	local testModule = script.Parent:FindFirstChild(testName)
	if not testModule then
		warn("❌ Test module not found:", testName)
		return false
	end
	
	-- 運行測試
	local success, result = pcall(function()
		return TestEZ.run({testModule})
	end)
	
	if success then
		print("✅ Test completed:", testName)
		if TEST_CONFIG.verbose then
			print("Results:", result)
		end
	else
		warn("❌ Test failed:", testName, result)
	end
	
	-- 清理測試環境
	TestRunner.cleanupTestEnvironment()
	
	return success
end

-- 設置測試環境
function TestRunner.setupTestEnvironment()
	print("🔧 Setting up test environment...")
	
	-- 設置全局測試標誌
	_G.TESTING = true
	_G.TEST_CONFIG = TEST_CONFIG
	
	-- 模擬 Matter World
	if not _G.MatterWorld then
		local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
		_G.MatterWorld = Matter.World.new()
		print("🌍 Created mock Matter World for testing")
	end
	
	-- 設置模擬數據
	if TEST_CONFIG.environment.useMockData then
		_G.MOCK_PLAYER_DATA = TEST_CONFIG.environment.mockPlayerData
		print("📊 Mock data loaded")
	end
	
	print("✅ Test environment ready")
end

-- 清理測試環境
function TestRunner.cleanupTestEnvironment()
	print("🧹 Cleaning up test environment...")
	
	-- 清理全局變量
	_G.TESTING = nil
	_G.TEST_CONFIG = nil
	_G.MOCK_PLAYER_DATA = nil
	
	-- 清理 Matter World
	if _G.MatterWorld then
		-- Matter World 沒有直接的清理方法，但我們可以重置引用
		_G.MatterWorld = nil
	end
	
	print("✅ Test environment cleaned")
end

-- 獲取所有測試模組
function TestRunner.getTestModules()
	local testModules = {}
	
	-- 掃描測試目錄
	local testFolders = {
		script.Parent.Services,
		script.Parent.ECS,
		script.Parent.Modules,
		script.Parent.Integration,
	}
	
	for _, folder in ipairs(testFolders) do
		if folder then
			for _, child in ipairs(folder:GetChildren()) do
				if child:IsA("ModuleScript") and child.Name:match("%.spec$") then
					table.insert(testModules, child)
				end
			end
		end
	end
	
	return testModules
end

-- 打印測試總結
function TestRunner.printSummary(total, passed, failed, results)
	print("\n" .. string.rep("=", 50))
	print("🧪 TEST SUMMARY")
	print(string.rep("=", 50))
	
	print(string.format("Total Tests: %d", total))
	print(string.format("Passed: %d (%.1f%%)", passed, total > 0 and (passed / total * 100) or 0))
	print(string.format("Failed: %d (%.1f%%)", failed, total > 0 and (failed / total * 100) or 0))
	
	if failed > 0 then
		print("\n❌ FAILED TESTS:")
		for moduleName, result in pairs(results) do
			if result.results then
				for _, testResult in ipairs(result.results) do
					if testResult.status ~= "Success" then
						print(string.format("  - %s: %s", moduleName, testResult.message or "Unknown error"))
					end
				end
			end
		end
	end
	
	if failed == 0 then
		print("\n🎉 All tests passed!")
	else
		print(string.format("\n⚠️ %d test(s) failed", failed))
	end
	
	print(string.rep("=", 50))
end

-- 運行性能測試
function TestRunner.runPerformanceTests()
	print("⚡ Running performance tests...")
	
	-- 這裡可以添加性能測試邏輯
	-- 例如：測試 ECS 系統的性能、大量實體的處理等
	
	print("✅ Performance tests completed")
end

-- 運行集成測試
function TestRunner.runIntegrationTests()
	print("🔗 Running integration tests...")
	
	-- 設置完整的測試環境
	TestRunner.setupTestEnvironment()
	
	-- 運行集成測試
	local integrationTests = script.Parent.Integration:GetChildren()
	
	for _, testModule in ipairs(integrationTests) do
		if testModule:IsA("ModuleScript") then
			print("🔍 Running integration test:", testModule.Name)
			
			local success, result = pcall(function()
				return require(testModule)
			end)
			
			if not success then
				warn("❌ Integration test failed:", testModule.Name, result)
			else
				print("✅ Integration test passed:", testModule.Name)
			end
		end
	end
	
	TestRunner.cleanupTestEnvironment()
	print("✅ Integration tests completed")
end

-- 監視模式（開發時使用）
function TestRunner.watchMode()
	print("👀 Starting test watch mode...")
	print("Tests will run automatically when files change")
	
	-- 這裡可以實現文件監視邏輯
	-- 在 Roblox 中可能需要手動觸發
	
	while true do
		wait(5) -- 每5秒檢查一次
		
		-- 檢查是否有變更（簡化版本）
		-- 實際實現可能需要更複雜的邏輯
		
		if _G.RUN_TESTS then
			_G.RUN_TESTS = false
			TestRunner.runAllTests()
		end
	end
end

return TestRunner
