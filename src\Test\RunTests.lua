--[[
	RunTests.lua - 測試執行腳本
	快速運行所有測試的入口點
]]

local TestRunner = require(script.Parent.TestRunner)

-- 運行所有測試
print("🚀 Starting comprehensive test suite...")
print("=" .. string.rep("=", 60))

-- 檢查測試環境
if not game:GetService("ReplicatedStorage"):FindFirstChild("Packages") then
	warn("❌ Packages not found! Please ensure all dependencies are installed.")
	return
end

-- 運行測試
local success = TestRunner.runAllTests()

if success then
	print("\n🎉 ALL TESTS PASSED!")
	print("Your battle system is ready for deployment!")
else
	print("\n⚠️ SOME TESTS FAILED!")
	print("Please review the failed tests and fix the issues.")
end

print("=" .. string.rep("=", 60))

-- 可選：運行集成測試
print("\n🔗 Running integration tests...")
TestRunner.runIntegrationTests()

-- 可選：運行性能測試
print("\n⚡ Running performance tests...")
TestRunner.runPerformanceTests()

print("\n✅ Test suite completed!")

return success
