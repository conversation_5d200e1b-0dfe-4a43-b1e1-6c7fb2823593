# 專案任務清單

## 階段一：架構重構與基礎設置

### [x] 1. 創建新的目錄結構 - 完成日期：2025-07-31
- [x] 1.1 創建 Knit 服務目錄結構
- [x] 1.2 創建 Matter ECS 系統目錄
- [x] 1.3 創建模型與資料模組目錄
- [x] 1.4 創建 UI 組件目錄
- [x] 1.5 創建測試目錄結構

### [x] 2. Matter ECS 基礎設置 - 完成日期：2025-07-31
- [x] 2.1 設置 Matter World 初始化
- [x] 2.2 創建基礎 Components
- [x] 2.3 創建基礎 Systems
- [x] 2.4 整合 ECS 與 Knit 框架

### [/] 3. 重構現有服務 - 進行中
- [x] 3.1 重構 PlayerService - 完成日期：2025-07-31
- [x] 3.2 重構 PetService - 完成日期：2025-07-31
- [ ] 3.3 重構 CombatService
- [ ] 3.4 創建 WeaponService
- [ ] 3.5 創建 GachaService
- [ ] 3.6 創建 ZoneService

## 階段二：ECS 系統實現

### [ ] 4. 實體與組件系統
- [ ] 4.1 實現 PlayerEntity
- [ ] 4.2 實現 PetEntity
- [ ] 4.3 實現 MonsterEntity
- [ ] 4.4 實現所有必要 Components

### [ ] 5. 核心系統實現
- [ ] 5.1 實現 SwordAttackSystem
- [ ] 5.2 實現 PetFollowSystem
- [ ] 5.3 實現 PetAttackSystem
- [ ] 5.4 實現 HealthSystem
- [ ] 5.5 實現 MonsterAISystem
- [ ] 5.6 實現 LifetimeSystem

## 階段三：資料與 UI 系統

### [ ] 6. 資料模組重構
- [ ] 6.1 創建 PetDatabase
- [ ] 6.2 創建 WeaponDatabase
- [ ] 6.3 創建 GachaPoolManager
- [ ] 6.4 重構 ProfileService 資料結構

### [ ] 7. UI 系統重建
- [ ] 7.1 重構 PetController
- [ ] 7.2 重構 CombatController
- [ ] 7.3 創建 GachaController
- [ ] 7.4 重構 UIController
- [ ] 7.5 實現 Fusion UI 組件

## 階段四：測試與優化

### [ ] 8. 測試系統
- [ ] 8.1 設置 TestEZ 測試框架
- [ ] 8.2 編寫服務單元測試
- [ ] 8.3 編寫 ECS 系統測試
- [ ] 8.4 創建 Matter Debug View

### [ ] 9. 性能優化
- [ ] 9.1 ECS 系統性能調優
- [ ] 9.2 網絡通信優化
- [ ] 9.3 記憶體使用優化
- [ ] 9.4 渲染性能優化

### [ ] 10. 最終整合與測試
- [ ] 10.1 整合所有系統
- [ ] 10.2 端到端測試
- [ ] 10.3 性能基準測試
- [ ] 10.4 文檔完善

## 備註
- 每個任務完成後請標記完成日期
- 發現新的子任務時請添加到相應階段
- 優先級：階段一 > 階段二 > 階段三 > 階段四

創建日期：2025-07-31
