--[[
	TenPullTest.lua - 十連抽專門測試
	直接測試十連抽功能，繞過UI和數據載入
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- 等待玩家加入
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🧪 Starting Ten Pull Test...")

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started, getting services...")
	
	-- 獲取服務
	local GachaService = Knit.GetService("GachaService")
	local PlayerService = Knit.GetService("PlayerService")
	
	if not GachaService then
		warn("❌ GachaService not found!")
		return
	end
	
	if not PlayerService then
		warn("❌ PlayerService not found!")
		return
	end
	
	print("✅ Services found")
	
	-- 監聽抽卡結果
	local resultReceived = false
	GachaService.GachaResult:Connect(function(results, poolType, isTenPull)
		resultReceived = true
		print("🎉 SUCCESS! Ten pull result received:")
		print("  Results count:", #results)
		print("  Pool type:", poolType)
		print("  Is ten pull:", isTenPull)
		
		if #results > 0 then
			print("  First result:", results[1].name, "(" .. results[1].rarity .. ")")
			if #results >= 10 then
				print("  Last result:", results[#results].name, "(" .. results[#results].rarity .. ")")
			end
		end
	end)
	
	-- 監聽金幣不足
	GachaService.InsufficientFunds:Connect(function(required, current)
		print("💰 Insufficient funds:")
		print("  Required:", required)
		print("  Current:", current)
		print("❌ Test failed - not enough coins")
	end)
	
	-- 等待服務完全初始化
	task.wait(2)
	
	-- 檢查玩家檔案
	local profile = PlayerService:GetPlayerProfile(player)
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Player profile found")
	print("  Current coins:", profile.Data.coins)
	
	-- 確保玩家有足夠金幣
	if profile.Data.coins < 900 then
		print("💰 Adding coins for test...")
		local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
		PlayerProfile.addCoins(player, 1000)
		print("  New coin balance:", profile.Data.coins)
	end
	
	print("\n🎰 Executing ten pull test...")
	print("  Firing TenPull signal...")
	
	-- 直接發送十連抽請求
	GachaService.TenPull:Fire("pet")
	
	-- 等待結果
	local waitTime = 0
	while not resultReceived and waitTime < 10 do
		task.wait(0.1)
		waitTime = waitTime + 0.1
	end
	
	if resultReceived then
		print("🎉 Ten Pull Test PASSED!")
	else
		print("❌ Ten Pull Test FAILED - No result received after 10 seconds")
		
		-- 嘗試單抽測試
		print("\n🧪 Trying single pull test...")
		local singleResultReceived = false
		
		local singleConnection
		singleConnection = GachaService.GachaResult:Connect(function(results, poolType, isTenPull)
			if not isTenPull then
				singleResultReceived = true
				print("✅ Single pull works:", results[1].name)
				singleConnection:Disconnect()
			end
		end)
		
		GachaService.SinglePull:Fire("pet")
		
		task.wait(3)
		
		if singleResultReceived then
			print("✅ Single pull works, but ten pull doesn't")
		else
			print("❌ Both single and ten pull failed")
		end
	end
	
	print("\n🧪 Test completed!")
	
end):catch(function(err)
	warn("❌ Test Error:", err)
end)

return true
