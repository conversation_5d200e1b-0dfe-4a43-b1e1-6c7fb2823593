--[[
	PetEntity.lua - 寵物實體工廠
	創建和管理寵物實體的標準化方法
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local DamageComponent = require(script.Parent.Parent.Components.DamageComponent)
local PetComponent = require(script.Parent.Parent.Components.PetComponent)
local FollowTargetComponent = require(script.Parent.Parent.Components.FollowTargetComponent)
local SwordSwingComponent = require(script.Parent.Parent.Components.SwordSwingComponent)
local TargetComponent = require(script.Parent.Parent.Components.TargetComponent)

-- 資料模組
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.PetDatabase)

local PetEntity = {}

-- AI 狀態組件
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local AIStateComponent = Matter.component("AIState", {
	currentState = "Follow", -- "Follow", "Attack", "Idle", "Return"
	lastStateChange = 0,
	stateData = {}, -- 狀態相關數據
	priority = 1, -- 狀態優先級
})

-- 創建寵物實體
function PetEntity.create(world, petId, ownerId, ownerEntityId, petData, spawnPosition)
	if not world or not petId or not ownerId then
		warn("❌ Invalid parameters for PetEntity.create")
		return nil
	end
	
	-- 獲取寵物配置
	local petConfig = PetDatabase.getPet(petId)
	if not petConfig then
		warn("❌ Pet config not found:", petId)
		return nil
	end
	
	-- 計算寵物屬性
	local level = petData and petData.level or 1
	local stats = PetDatabase.calculateStats(petId, level)
	
	-- 設置初始位置
	local position = spawnPosition or Vector3.new(0, 10, 0)
	
	-- 創建寵物實體
	local entityId = world:spawn(
		PetComponent({
			petId = petId,
			ownerId = ownerId,
			level = level,
			experience = petData and petData.experience or 0,
			experienceToNext = level * 50, -- 寵物升級需要的經驗較少
			rarity = petConfig.rarity,
			isActive = true,
			summonTime = tick(),
		}),
		
		PositionComponent({
			position = position,
			rotation = CFrame.new(position),
			lastPosition = position,
			velocity = Vector3.new(),
		}),
		
		HealthComponent({
			current = stats.health,
			maximum = stats.health,
			regeneration = 0.5, -- 寵物回血較慢
			lastDamageTime = 0,
			isDead = false,
		}),
		
		DamageComponent({
			attack = stats.attack,
			defense = stats.defense,
			criticalChance = 0.05 + (level * 0.005), -- 等級越高暴擊率越高
			criticalMultiplier = 1.5 + (level * 0.05), -- 等級越高暴擊倍率越高
			elementalType = "None",
			elementalDamage = 0,
		}),
		
		FollowTargetComponent({
			targetId = ownerEntityId or 0,
			followDistance = 5,
			speed = stats.speed or 14,
			isFollowing = true,
			maxDistance = 50,
			lastTargetPosition = position,
		}),
		
		SwordSwingComponent({
			isSwinging = false,
			swingDuration = 0.4, -- 寵物攻擊較快
			swingStartTime = 0,
			range = 6, -- 寵物攻擊範圍較小
			damage = stats.attack,
			weaponId = "PetAttack",
			hasHit = {},
		}),
		
		TargetComponent({
			targetId = 0,
			targetType = "Monster",
			lastTargetTime = 0,
			attackRange = 6,
			canAttack = true,
			lastAttackTime = 0,
			attackCooldown = 2.0, -- 寵物攻擊冷卻較長
		}),
		
		AIStateComponent({
			currentState = "Follow",
			lastStateChange = tick(),
			stateData = {},
			priority = 1,
		})
	)
	
	print("🐾 Created PetEntity:", entityId, "for pet", petId)
	return entityId
end

-- 更新寵物數據
function PetEntity.updateData(world, entityId, petData)
	if not world or not entityId or not petData then return false end
	
	local petComponent = world:get(entityId, PetComponent)
	if petComponent then
		world:insert(entityId, petComponent:patch({
			level = petData.level or petComponent.level,
			experience = petData.experience or petComponent.experience,
			experienceToNext = (petData.level or petComponent.level) * 50,
		}))
		
		-- 重新計算屬性
		local stats = PetDatabase.calculateStats(petComponent.petId, petData.level or petComponent.level)
		PetEntity.updateStats(world, entityId, stats)
		
		return true
	end
	
	return false
end

-- 更新寵物屬性
function PetEntity.updateStats(world, entityId, stats)
	if not world or not entityId or not stats then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	local damageComponent = world:get(entityId, DamageComponent)
	
	if healthComponent then
		local healthPercent = healthComponent.current / healthComponent.maximum
		world:insert(entityId, healthComponent:patch({
			maximum = stats.health,
			current = math.floor(stats.health * healthPercent), -- 保持血量百分比
		}))
	end
	
	if damageComponent then
		world:insert(entityId, damageComponent:patch({
			attack = stats.attack,
			defense = stats.defense,
		}))
	end
	
	return true
end

-- 設置寵物跟隨目標
function PetEntity.setFollowTarget(world, entityId, targetEntityId, followDistance)
	if not world or not entityId then return false end
	
	local followComponent = world:get(entityId, FollowTargetComponent)
	if followComponent then
		world:insert(entityId, followComponent:patch({
			targetId = targetEntityId or 0,
			followDistance = followDistance or followComponent.followDistance,
			isFollowing = targetEntityId ~= nil,
		}))
		return true
	end
	
	return false
end

-- 設置寵物 AI 狀態
function PetEntity.setAIState(world, entityId, newState, stateData, priority)
	if not world or not entityId then return false end
	
	local aiComponent = world:get(entityId, AIStateComponent)
	if aiComponent then
		-- 檢查優先級
		if priority and priority < aiComponent.priority then
			return false -- 優先級不夠，不切換狀態
		end
		
		world:insert(entityId, aiComponent:patch({
			currentState = newState,
			lastStateChange = tick(),
			stateData = stateData or {},
			priority = priority or 1,
		}))
		
		print("🐾 Pet AI state changed to:", newState)
		return true
	end
	
	return false
end

-- 設置寵物攻擊目標
function PetEntity.setAttackTarget(world, entityId, targetEntityId)
	if not world or not entityId then return false end
	
	local targetComponent = world:get(entityId, TargetComponent)
	if targetComponent then
		world:insert(entityId, targetComponent:patch({
			targetId = targetEntityId or 0,
			lastTargetTime = tick(),
		}))
		
		-- 切換到攻擊狀態
		if targetEntityId then
			PetEntity.setAIState(world, entityId, "Attack", {targetId = targetEntityId}, 2)
		else
			PetEntity.setAIState(world, entityId, "Follow", {}, 1)
		end
		
		return true
	end
	
	return false
end

-- 增加寵物經驗值
function PetEntity.addExperience(world, entityId, expAmount)
	if not world or not entityId then return false end
	
	local petComponent = world:get(entityId, PetComponent)
	if petComponent then
		local newExp = petComponent.experience + expAmount
		local newLevel = petComponent.level
		local expToNext = petComponent.experienceToNext
		
		-- 檢查升級
		local leveledUp = false
		while newExp >= expToNext do
			newExp = newExp - expToNext
			newLevel = newLevel + 1
			expToNext = newLevel * 50
			leveledUp = true
		end
		
		world:insert(entityId, petComponent:patch({
			experience = newExp,
			level = newLevel,
			experienceToNext = expToNext,
		}))
		
		-- 如果升級了，更新屬性
		if leveledUp then
			local stats = PetDatabase.calculateStats(petComponent.petId, newLevel)
			PetEntity.updateStats(world, entityId, stats)
			return newLevel -- 返回新等級表示升級了
		end
		
		return true
	end
	
	return false
end

-- 治療寵物
function PetEntity.heal(world, entityId, healAmount)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	if healthComponent and not healthComponent.isDead then
		local newHealth = math.min(healthComponent.maximum, healthComponent.current + healAmount)
		world:insert(entityId, healthComponent:patch({
			current = newHealth,
		}))
		return newHealth
	end
	
	return false
end

-- 設置寵物活躍狀態
function PetEntity.setActive(world, entityId, isActive)
	if not world or not entityId then return false end
	
	local petComponent = world:get(entityId, PetComponent)
	if petComponent then
		world:insert(entityId, petComponent:patch({
			isActive = isActive,
		}))
		
		-- 如果設為非活躍，停止所有行為
		if not isActive then
			PetEntity.setAIState(world, entityId, "Idle", {}, 0)
			PetEntity.setAttackTarget(world, entityId, nil)
		else
			PetEntity.setAIState(world, entityId, "Follow", {}, 1)
		end
		
		return true
	end
	
	return false
end

-- 獲取寵物數據
function PetEntity.getData(world, entityId)
	if not world or not entityId then return nil end
	
	local petComponent = world:get(entityId, PetComponent)
	local healthComponent = world:get(entityId, HealthComponent)
	local damageComponent = world:get(entityId, DamageComponent)
	local positionComponent = world:get(entityId, PositionComponent)
	local aiComponent = world:get(entityId, AIStateComponent)
	
	return {
		pet = petComponent,
		health = healthComponent,
		damage = damageComponent,
		position = positionComponent,
		ai = aiComponent,
	}
end

-- 檢查寵物是否存活
function PetEntity.isAlive(world, entityId)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	local petComponent = world:get(entityId, PetComponent)
	
	return healthComponent and petComponent and 
		   not healthComponent.isDead and 
		   healthComponent.current > 0 and 
		   petComponent.isActive
end

-- 復活寵物
function PetEntity.revive(world, entityId, healthPercent)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	if healthComponent and healthComponent.isDead then
		local newHealth = math.floor(healthComponent.maximum * (healthPercent or 0.5))
		world:insert(entityId, healthComponent:patch({
			current = newHealth,
			isDead = false,
		}))
		
		-- 重新激活寵物
		PetEntity.setActive(world, entityId, true)
		return true
	end
	
	return false
end

-- 獲取寵物配置
function PetEntity.getConfig(petId)
	return PetDatabase.getPet(petId)
end

-- 計算寵物屬性
function PetEntity.calculateStats(petId, level)
	return PetDatabase.calculateStats(petId, level)
end

-- 導出組件供其他模組使用
PetEntity.AIStateComponent = AIStateComponent

return PetEntity
