--[[
	血量系統
	處理血量回復、死亡檢測等邏輯
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(script.Parent.Parent.Components)

local function HealthSystem(world, deltaTime)
	-- 查詢所有有血量的實體
	for entityId, health in world:query(Components.Health) do
		local needsUpdate = false
		local newHealth = health.current
		
		-- 處理血量回復
		if health.regeneration > 0 and health.current < health.maximum then
			newHealth = math.min(health.maximum, health.current + health.regeneration * deltaTime)
			needsUpdate = true
		end
		
		-- 檢查死亡
		if health.current <= 0 then
			-- 處理死亡邏輯
			local player = world:get(entityId, Components.Player)
			local pet = world:get(entityId, Components.Pet)
			local monster = world:get(entityId, Components.Monster)
			
			if player then
				-- 玩家死亡處理
				print("💀 Player died:", player.displayName)
				
				-- 重置血量（復活）
				newHealth = health.maximum
				needsUpdate = true
				
				-- 可以在這裡添加死亡懲罰邏輯
				-- 例如：扣除經驗值、金幣等
				
			elseif pet then
				-- 寵物死亡處理
				print("💀 Pet died:", pet.petId)
				
				-- 寵物死亡後可能需要收回或復活
				-- 這裡可以發送事件給 PetService 處理
				
			elseif monster then
				-- 怪物死亡處理
				print("💀 Monster died:", monster.monsterId)
				
				-- 怪物死亡後給予獎勵
				-- 這裡可以發送事件給 CombatService 處理獎勵分配
				
				-- 標記怪物為待移除
				world:insert(entityId, Components.Lifetime({
					duration = 0,
					startTime = tick(),
					destroyOnExpire = true,
				}))
			end
		end
		
		-- 更新血量
		if needsUpdate then
			world:insert(entityId, health:patch({
				current = newHealth
			}))
		end
		
		-- 同步血量到 UI（如果是玩家或寵物）
		if world:get(entityId, Components.Player) or world:get(entityId, Components.Pet) then
			-- 這裡可以發送事件給客戶端更新 UI
			-- 例如：通過 RemoteEvent 發送血量更新
		end
	end
end

return HealthSystem
