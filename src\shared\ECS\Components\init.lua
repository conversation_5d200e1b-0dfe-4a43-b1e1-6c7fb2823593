--[[
	ECS 組件定義
	定義所有 Matter ECS 組件
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)

local Components = {}

-- 位置組件
Components.Position = Matter.component("Position", {
	position = Vector3.new(),
	rotation = CFrame.new(),
})

-- 血量組件
Components.Health = Matter.component("Health", {
	current = 100,
	maximum = 100,
	regeneration = 0, -- 每秒回血量
})

-- 傷害組件
Components.Damage = Matter.component("Damage", {
	attack = 20,
	defense = 5,
	criticalChance = 0.1,
	criticalMultiplier = 2.0,
})

-- 寵物組件
Components.Pet = Matter.component("Pet", {
	petId = "",
	ownerId = 0,
	level = 1,
	experience = 0,
	rarity = "Common",
})

-- 跟隨目標組件
Components.FollowTarget = Matter.component("FollowTarget", {
	targetId = 0,
	followDistance = 5,
	speed = 16,
	isFollowing = true,
})

-- 劍擊組件
Components.SwordSwing = Matter.component("SwordSwing", {
	isSwinging = false,
	swingDuration = 0.5,
	swingStartTime = 0,
	range = 10,
	damage = 20,
})

-- 目標組件
Components.Target = Matter.component("Target", {
	targetId = 0,
	targetType = "Monster", -- "Player", "Pet", "Monster"
	lastTargetTime = 0,
})

-- 玩家組件
Components.Player = Matter.component("Player", {
	userId = 0,
	displayName = "",
	level = 1,
	experience = 0,
	coins = 0,
})

-- 怪物組件
Components.Monster = Matter.component("Monster", {
	monsterId = "",
	aiState = "Idle", -- "Idle", "Patrol", "Chase", "Attack"
	patrolRadius = 20,
	chaseRadius = 15,
	attackRadius = 5,
	lastStateChange = 0,
})

-- 生命週期組件
Components.Lifetime = Matter.component("Lifetime", {
	duration = 60, -- 存活時間（秒）
	startTime = 0,
	destroyOnExpire = true,
})

-- 武器組件
Components.Weapon = Matter.component("Weapon", {
	weaponId = "",
	weaponType = "Sword",
	damage = 20,
	range = 10,
	attackSpeed = 1.0,
	isEquipped = false,
})

-- 移動組件
Components.Movement = Matter.component("Movement", {
	velocity = Vector3.new(),
	speed = 16,
	acceleration = 50,
	friction = 0.9,
})

-- 碰撞組件
Components.Collision = Matter.component("Collision", {
	size = Vector3.new(4, 6, 2),
	shape = "Box", -- "Box", "Sphere", "Capsule"
	isTrigger = false,
})

-- 動畫組件
Components.Animation = Matter.component("Animation", {
	currentAnimation = "Idle",
	animationSpeed = 1.0,
	isLooping = true,
	animationTrack = nil,
})

-- 特效組件
Components.Effect = Matter.component("Effect", {
	effectType = "Explosion",
	duration = 1.0,
	startTime = 0,
	intensity = 1.0,
})

return Components
