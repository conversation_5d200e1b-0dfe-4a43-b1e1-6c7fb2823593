--[[
	GachaPoolManager.lua - 抽卡池管理器
	管理不同類型的抽卡池、機率配置、限時活動
]]

local PetDatabase = require(script.Parent.PetDatabase)
local WeaponDatabase = require(script.Parent.WeaponDatabase)

local GachaPoolManager = {}

-- 抽卡池類型
local POOL_TYPES = {
	PET = "pet",
	WEAPON = "weapon",
	MIXED = "mixed",
	LIMITED = "limited",
}

-- 預設抽卡池配置
local DEFAULT_POOLS = {
	-- 標準寵物池
	standard_pet = {
		id = "standard_pet",
		name = "標準寵物池",
		type = POOL_TYPES.PET,
		isActive = true,
		isLimited = false,
		startTime = 0,
		endTime = 0,
		description = "包含所有常駐寵物的標準池",
		
		-- 機率配置
		rates = {
			Common = 0.60,
			Uncommon = 0.25,
			Rare = 0.12,
			Epic = 0.025,
			Legendary = 0.005,
		},
		
		-- 保底配置
		pity = {
			rare = {threshold = 10, guarantee = "Rare"},
			epic = {threshold = 50, guarantee = "Epic"},
			legendary = {threshold = 100, guarantee = "Legendary"},
		},
		
		-- 包含的物品
		items = {
			Common = {"Slime", "Wolf"},
			Uncommon = {"FireSpirit", "IceWolf"},
			Rare = {"DragonHatchling"},
			Epic = {"PhoenixChick"},
			Legendary = {"CelestialDragon"},
		},
		
		-- 特殊規則
		rules = {
			guaranteedRareIn10 = true,
			firstTimeBonus = false,
		},
	},
	
	-- 標準武器池
	standard_weapon = {
		id = "standard_weapon",
		name = "標準武器池",
		type = POOL_TYPES.WEAPON,
		isActive = true,
		isLimited = false,
		startTime = 0,
		endTime = 0,
		description = "包含所有常駐武器的標準池",
		
		rates = {
			Common = 0.60,
			Uncommon = 0.25,
			Rare = 0.12,
			Epic = 0.025,
			Legendary = 0.005,
		},
		
		pity = {
			rare = {threshold = 10, guarantee = "Rare"},
			epic = {threshold = 50, guarantee = "Epic"},
			legendary = {threshold = 100, guarantee = "Legendary"},
		},
		
		items = {
			Common = {"WoodenSword", "IronSword"},
			Uncommon = {"SteelBlade", "FlameEdge"},
			Rare = {"DragonFang"},
			Epic = {"Excalibur"},
			Legendary = {"CosmicBlade"},
		},
		
		rules = {
			guaranteedRareIn10 = true,
			firstTimeBonus = false,
		},
	},
	
	-- 混合池
	mixed_pool = {
		id = "mixed_pool",
		name = "混合池",
		type = POOL_TYPES.MIXED,
		isActive = true,
		isLimited = false,
		startTime = 0,
		endTime = 0,
		description = "包含寵物和武器的混合池",
		
		rates = {
			Common = 0.60,
			Uncommon = 0.25,
			Rare = 0.12,
			Epic = 0.025,
			Legendary = 0.005,
		},
		
		pity = {
			rare = {threshold = 10, guarantee = "Rare"},
			epic = {threshold = 50, guarantee = "Epic"},
			legendary = {threshold = 100, guarantee = "Legendary"},
		},
		
		-- 混合池的物品分配
		itemDistribution = {
			pet = 0.7, -- 70% 機率是寵物
			weapon = 0.3, -- 30% 機率是武器
		},
		
		items = {
			pet = {
				Common = {"Slime", "Wolf"},
				Uncommon = {"FireSpirit", "IceWolf"},
				Rare = {"DragonHatchling"},
				Epic = {"PhoenixChick"},
				Legendary = {"CelestialDragon"},
			},
			weapon = {
				Common = {"WoodenSword", "IronSword"},
				Uncommon = {"SteelBlade", "FlameEdge"},
				Rare = {"DragonFang"},
				Epic = {"Excalibur"},
				Legendary = {"CosmicBlade"},
			},
		},
		
		rules = {
			guaranteedRareIn10 = true,
			firstTimeBonus = false,
		},
	},
}

-- 限時活動池範例
local LIMITED_POOLS = {
	dragon_festival = {
		id = "dragon_festival",
		name = "龍族祭典限定池",
		type = POOL_TYPES.LIMITED,
		isActive = false,
		isLimited = true,
		startTime = 0, -- 需要設置實際時間
		endTime = 0,   -- 需要設置實際時間
		description = "龍族祭典期間限定，龍族寵物出現率UP！",
		
		rates = {
			Common = 0.50,
			Uncommon = 0.25,
			Rare = 0.18, -- 提高稀有度機率
			Epic = 0.05,
			Legendary = 0.02,
		},
		
		pity = {
			rare = {threshold = 8, guarantee = "Rare"}, -- 降低保底次數
			epic = {threshold = 40, guarantee = "Epic"},
			legendary = {threshold = 80, guarantee = "Legendary"},
		},
		
		items = {
			Common = {"Slime", "Wolf"},
			Uncommon = {"FireSpirit"},
			Rare = {"DragonHatchling"}, -- 重點推薦
			Epic = {"PhoenixChick"},
			Legendary = {"CelestialDragon"}, -- 重點推薦
		},
		
		-- 特殊獎勵
		featuredItems = {"DragonHatchling", "CelestialDragon"},
		featuredRateUp = 2.0, -- 特色物品機率翻倍
		
		rules = {
			guaranteedRareIn10 = true,
			firstTimeBonus = true, -- 首次十連必出Epic以上
			limitedRewards = {
				{pulls = 50, reward = "DragonHatchling"},
				{pulls = 100, reward = "CelestialDragon"},
			},
		},
	},
}

-- 當前活躍的抽卡池
local activePools = {}

-- 初始化抽卡池管理器
function GachaPoolManager.initialize()
	-- 載入預設池
	for poolId, poolData in pairs(DEFAULT_POOLS) do
		activePools[poolId] = poolData
	end
	
	-- 檢查限時池是否應該激活
	GachaPoolManager.updateLimitedPools()
	
	print("🎰 GachaPoolManager initialized with", #activePools, "pools")
end

-- 更新限時池狀態
function GachaPoolManager.updateLimitedPools()
	local currentTime = os.time()
	
	for poolId, poolData in pairs(LIMITED_POOLS) do
		local shouldBeActive = currentTime >= poolData.startTime and currentTime <= poolData.endTime
		
		if shouldBeActive and not activePools[poolId] then
			-- 激活限時池
			activePools[poolId] = poolData
			activePools[poolId].isActive = true
			print("🎰 Limited pool activated:", poolData.name)
		elseif not shouldBeActive and activePools[poolId] then
			-- 停用限時池
			activePools[poolId] = nil
			print("🎰 Limited pool deactivated:", poolData.name)
		end
	end
end

-- 獲取可用的抽卡池
function GachaPoolManager.getAvailablePools()
	local available = {}
	for poolId, poolData in pairs(activePools) do
		if poolData.isActive then
			available[poolId] = {
				id = poolData.id,
				name = poolData.name,
				type = poolData.type,
				description = poolData.description,
				isLimited = poolData.isLimited,
				endTime = poolData.endTime,
			}
		end
	end
	return available
end

-- 獲取指定抽卡池
function GachaPoolManager.getPool(poolId)
	return activePools[poolId]
end

-- 從池中選擇物品
function GachaPoolManager.selectFromPool(poolId, rarity, playerPityData)
	local pool = activePools[poolId]
	if not pool then
		warn("Pool not found:", poolId)
		return nil
	end
	
	local items
	if pool.type == POOL_TYPES.MIXED then
		-- 混合池需要先決定是寵物還是武器
		local itemType = math.random() <= pool.itemDistribution.pet and "pet" or "weapon"
		items = pool.items[itemType][rarity]
	else
		items = pool.items[rarity]
	end
	
	if not items or #items == 0 then
		warn("No items found for rarity:", rarity, "in pool:", poolId)
		return nil
	end
	
	-- 處理特色物品機率UP
	if pool.featuredItems and pool.featuredRateUp then
		local featuredInRarity = {}
		local normalInRarity = {}
		
		for _, itemId in ipairs(items) do
			local isFeatured = false
			for _, featuredId in ipairs(pool.featuredItems) do
				if itemId == featuredId then
					isFeatured = true
					break
				end
			end
			
			if isFeatured then
				-- 添加多次以增加機率
				for i = 1, pool.featuredRateUp do
					table.insert(featuredInRarity, itemId)
				end
			else
				table.insert(normalInRarity, itemId)
			end
		end
		
		-- 合併列表
		local weightedItems = {}
		for _, item in ipairs(featuredInRarity) do
			table.insert(weightedItems, item)
		end
		for _, item in ipairs(normalInRarity) do
			table.insert(weightedItems, item)
		end
		
		items = weightedItems
	end
	
	-- 隨機選擇
	local selectedItem = items[math.random(1, #items)]
	
	-- 確定物品類型
	local itemType = pool.type
	if pool.type == POOL_TYPES.MIXED then
		-- 檢查是寵物還是武器
		if PetDatabase.getPet(selectedItem) then
			itemType = "pet"
		elseif WeaponDatabase.getWeapon(selectedItem) then
			itemType = "weapon"
		end
	end
	
	return {
		id = selectedItem,
		type = itemType,
		rarity = rarity,
		poolId = poolId,
	}
end

-- 檢查保底
function GachaPoolManager.checkPity(poolId, playerPityData)
	local pool = activePools[poolId]
	if not pool or not pool.pity then
		return nil
	end
	
	-- 檢查各級保底
	for rarityLevel, pityConfig in pairs(pool.pity) do
		local pityCount = playerPityData[rarityLevel] or 0
		if pityCount >= pityConfig.threshold then
			return pityConfig.guarantee
		end
	end
	
	return nil
end

-- 應用特殊規則
function GachaPoolManager.applySpecialRules(poolId, pullCount, isFirstTime)
	local pool = activePools[poolId]
	if not pool or not pool.rules then
		return nil
	end
	
	local rules = pool.rules
	
	-- 首次十連獎勵
	if isFirstTime and rules.firstTimeBonus and pullCount == 10 then
		return "Epic" -- 保證Epic以上
	end
	
	-- 十連保底稀有
	if rules.guaranteedRareIn10 and pullCount == 10 then
		return "Rare" -- 保證最後一抽至少是稀有
	end
	
	return nil
end

-- 獲取池子統計信息
function GachaPoolManager.getPoolStats(poolId)
	local pool = activePools[poolId]
	if not pool then
		return nil
	end
	
	local stats = {
		totalItems = 0,
		itemsByRarity = {},
	}
	
	if pool.type == POOL_TYPES.MIXED then
		for itemType, rarityItems in pairs(pool.items) do
			for rarity, items in pairs(rarityItems) do
				stats.itemsByRarity[rarity] = (stats.itemsByRarity[rarity] or 0) + #items
				stats.totalItems = stats.totalItems + #items
			end
		end
	else
		for rarity, items in pairs(pool.items) do
			stats.itemsByRarity[rarity] = #items
			stats.totalItems = stats.totalItems + #items
		end
	end
	
	return stats
end

-- 創建自定義池
function GachaPoolManager.createCustomPool(poolData)
	if not poolData.id then
		warn("Pool must have an ID")
		return false
	end
	
	activePools[poolData.id] = poolData
	print("🎰 Custom pool created:", poolData.name)
	return true
end

-- 移除池
function GachaPoolManager.removePool(poolId)
	if activePools[poolId] then
		activePools[poolId] = nil
		print("🎰 Pool removed:", poolId)
		return true
	end
	return false
end

-- 導出常量
GachaPoolManager.POOL_TYPES = POOL_TYPES

return GachaPoolManager
