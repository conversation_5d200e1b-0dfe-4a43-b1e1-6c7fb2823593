--[[
	LifetimeSystem.lua - 生命週期系統
	處理實體的存活時間控制（如召喚獸、特效等）
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local PetComponent = require(script.Parent.Parent.Components.PetComponent)

-- 生命週期組件
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local LifetimeComponent = Matter.component("Lifetime", {
	duration = 60, -- 存活時間（秒）
	startTime = 0,
	destroyOnExpire = true,
	fadeOut = false, -- 是否淡出
	fadeStartTime = 0,
})

local function LifetimeSystem(world, deltaTime)
	local currentTime = tick()
	
	-- 查詢所有有生命週期的實體
	for entityId, lifetime in world:query(LifetimeComponent) do
		local timeAlive = currentTime - lifetime.startTime
		
		-- 檢查是否到期
		if timeAlive >= lifetime.duration then
			if lifetime.destroyOnExpire then
				-- 檢查是否是寵物實體
				local pet = world:get(entityId, PetComponent)
				if pet then
					-- 寵物到期處理
					print("🐾 Pet lifetime expired:", pet.petId)
					
					-- 在 workspace 中查找對應的模型並銷毀
					local petModel = workspace:FindFirstChild("Pet_" .. entityId)
					if petModel then
						petModel:Destroy()
					end
				end
				
				-- 銷毀實體
				world:despawn(entityId)
				print("⏰ Entity", entityId, "destroyed due to lifetime expiration")
			else
				-- 不銷毀，只是標記為過期
				world:insert(entityId, lifetime:patch({
					destroyOnExpire = false,
				}))
			end
		elseif lifetime.fadeOut and not lifetime.fadeStartTime then
			-- 開始淡出效果（在剩餘10%時間時開始）
			local fadeStartRatio = 0.9
			if timeAlive >= lifetime.duration * fadeStartRatio then
				world:insert(entityId, lifetime:patch({
					fadeStartTime = currentTime,
				}))
				
				-- 開始淡出效果
				local position = world:get(entityId, PositionComponent)
				if position then
					-- 這裡可以添加淡出視覺效果
					print("👻 Entity", entityId, "starting fade out")
				end
			end
		end
	end
	
	-- 處理死亡實體的清理
	local healthComponent = require(script.Parent.Parent.Components.HealthComponent)
	for entityId, health in world:query(healthComponent) do
		if health.isDead then
			-- 檢查死亡時間
			local timeSinceDeath = currentTime - health.lastDamageTime
			
			-- 死亡5秒後清理
			if timeSinceDeath >= 5 then
				-- 檢查是否是寵物
				local pet = world:get(entityId, PetComponent)
				if pet then
					-- 寵物死亡處理 - 不立即銷毀，而是設置為非活躍
					world:insert(entityId, pet:patch({
						isActive = false,
					}))
					
					-- 重置血量準備復活
					world:insert(entityId, health:patch({
						current = health.maximum * 0.5, -- 復活時50%血量
						isDead = false,
					}))
					
					print("🐾 Pet", pet.petId, "revived with 50% health")
				else
					-- 非寵物實體直接銷毀
					world:despawn(entityId)
					print("💀 Dead entity", entityId, "cleaned up")
				end
			end
		end
	end
end

-- 添加生命週期組件到實體
local function addLifetimeComponent(world, entityId, duration, destroyOnExpire, fadeOut)
	world:insert(entityId, LifetimeComponent({
		duration = duration or 60,
		startTime = tick(),
		destroyOnExpire = destroyOnExpire ~= false, -- 默認為true
		fadeOut = fadeOut or false,
		fadeStartTime = 0,
	}))
end

-- 延長實體生命週期
local function extendLifetime(world, entityId, additionalTime)
	local lifetime = world:get(entityId, LifetimeComponent)
	if lifetime then
		world:insert(entityId, lifetime:patch({
			duration = lifetime.duration + additionalTime,
		}))
		return true
	end
	return false
end

-- 設置實體為永久存在
local function makeEntityPermanent(world, entityId)
	local lifetime = world:get(entityId, LifetimeComponent)
	if lifetime then
		world:remove(entityId, LifetimeComponent)
		return true
	end
	return false
end

-- 導出輔助函數到全局
_G.LifetimeSystemUtils = {
	addLifetime = addLifetimeComponent,
	extendLifetime = extendLifetime,
	makeEntityPermanent = makeEntityPermanent,
	LifetimeComponent = LifetimeComponent,
}

return LifetimeSystem
