--[[
	ZoneService.lua - 區域服務
	偵測進入戰鬥區/安全區，結合 ZonePlus
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local ZonePlus = require(game:GetService("ReplicatedStorage").Packages.zoneplus)

local ZoneService = Knit.CreateService({
	Name = "ZoneService",
	Client = {
		-- 客戶端事件
		ZoneEntered = Knit.CreateSignal(),
		ZoneExited = Knit.CreateSignal(),
		ZoneStatusChanged = Knit.CreateSignal(),
	},
})

-- 區域配置
local ZONE_CONFIG = {
	SafeZone = {
		name = "安全區",
		type = "safe",
		allowCombat = false,
		allowPvP = false,
		regenerationBonus = 2.0, -- 血量回復加成
		effects = {
			"NoAttack", -- 禁止攻擊
			"FastRegen", -- 快速回血
		},
	},
	
	CombatZone = {
		name = "戰鬥區",
		type = "combat",
		allowCombat = true,
		allowPvP = false,
		expBonus = 1.5, -- 經驗值加成
		effects = {
			"CombatEnabled", -- 允許戰鬥
			"ExpBonus", -- 經驗加成
		},
	},
	
	PvPZone = {
		name = "PvP區域",
		type = "pvp",
		allowCombat = true,
		allowPvP = true,
		expBonus = 2.0,
		coinBonus = 2.0,
		effects = {
			"PvPEnabled", -- 允許PvP
			"HighReward", -- 高獎勵
		},
	},
	
	BossZone = {
		name = "Boss區域",
		type = "boss",
		allowCombat = true,
		allowPvP = false,
		expBonus = 3.0,
		coinBonus = 3.0,
		effects = {
			"BossEncounter", -- Boss戰
			"HighDifficulty", -- 高難度
		},
	},
}

-- 私有變量
local zones = {} -- ZonePlus 區域對象
local playerZones = {} -- 玩家當前所在區域 {[player] = zoneType}

function ZoneService:KnitStart()
	print("🗺️ ZoneService started")
	
	-- 初始化區域
	self:_initializeZones()
	
	-- 監聽玩家加入/離開
	Players.PlayerAdded:Connect(function(player)
		self:_onPlayerAdded(player)
	end)
	
	Players.PlayerRemoving:Connect(function(player)
		self:_onPlayerRemoving(player)
	end)
end

function ZoneService:KnitInit()
	-- 獲取其他服務
	self.PlayerService = Knit.GetService("PlayerService")
	self.CombatService = Knit.GetService("CombatService")
end

-- 初始化區域
function ZoneService:_initializeZones()
	-- 在 workspace 中查找區域部件
	local zoneFolder = workspace:FindFirstChild("Zones")
	if not zoneFolder then
		warn("❌ Zones folder not found in workspace")
		return
	end
	
	for _, zonePart in pairs(zoneFolder:GetChildren()) do
		if zonePart:IsA("BasePart") then
			local zoneType = zonePart.Name
			local zoneConfig = ZONE_CONFIG[zoneType]
			
			if zoneConfig then
				-- 創建 ZonePlus 區域
				local zone = ZonePlus.new(zonePart)
				zones[zoneType] = zone
				
				-- 監聽玩家進入區域
				zone.playerEntered:Connect(function(player)
					self:_onPlayerEnteredZone(player, zoneType, zoneConfig)
				end)
				
				-- 監聽玩家離開區域
				zone.playerExited:Connect(function(player)
					self:_onPlayerExitedZone(player, zoneType, zoneConfig)
				end)
				
				print("🗺️ Zone initialized:", zoneType, "at", zonePart.Position)
			else
				warn("❌ Unknown zone type:", zoneType)
			end
		end
	end
	
	print("🗺️ Total zones initialized:", #zones)
end

-- 玩家加入處理
function ZoneService:_onPlayerAdded(player)
	playerZones[player] = nil
	
	-- 等待角色生成後檢查初始區域
	if player.Character then
		self:_checkPlayerInitialZone(player)
	else
		player.CharacterAdded:Connect(function()
			self:_checkPlayerInitialZone(player)
		end)
	end
end

-- 玩家離開處理
function ZoneService:_onPlayerRemoving(player)
	playerZones[player] = nil
end

-- 檢查玩家初始區域
function ZoneService:_checkPlayerInitialZone(player)
	wait(1) -- 等待角色完全載入
	
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return
	end
	
	local playerPosition = player.Character.HumanoidRootPart.Position
	
	-- 檢查玩家是否在任何區域內
	for zoneType, zone in pairs(zones) do
		if zone:findLocalPlayer() == player then
			local zoneConfig = ZONE_CONFIG[zoneType]
			self:_onPlayerEnteredZone(player, zoneType, zoneConfig)
			break
		end
	end
end

-- 玩家進入區域
function ZoneService:_onPlayerEnteredZone(player, zoneType, zoneConfig)
	local previousZone = playerZones[player]
	playerZones[player] = zoneType
	
	print("🗺️ Player", player.Name, "entered zone:", zoneType)
	
	-- 應用區域效果
	self:_applyZoneEffects(player, zoneConfig, true)
	
	-- 通知客戶端
	self.Client.ZoneEntered:Fire(player, zoneType, zoneConfig.name, zoneConfig.effects)
	self.Client.ZoneStatusChanged:Fire(player, zoneType, zoneConfig)
	
	-- 如果從其他區域進入，先移除之前的效果
	if previousZone and ZONE_CONFIG[previousZone] then
		self:_applyZoneEffects(player, ZONE_CONFIG[previousZone], false)
	end
end

-- 玩家離開區域
function ZoneService:_onPlayerExitedZone(player, zoneType, zoneConfig)
	if playerZones[player] == zoneType then
		playerZones[player] = nil
		
		print("🗺️ Player", player.Name, "exited zone:", zoneType)
		
		-- 移除區域效果
		self:_applyZoneEffects(player, zoneConfig, false)
		
		-- 通知客戶端
		self.Client.ZoneExited:Fire(player, zoneType, zoneConfig.name)
		self.Client.ZoneStatusChanged:Fire(player, nil, nil)
	end
end

-- 應用區域效果
function ZoneService:_applyZoneEffects(player, zoneConfig, isEntering)
	local playerEntityId = self.PlayerService:GetPlayerEntityId(player)
	if not playerEntityId then return end
	
	-- 獲取 Matter World
	local world = _G.MatterWorld
	if not world then return end
	
	local HealthComponent = require(game:GetService("ReplicatedStorage").ECS.Components.HealthComponent)
	local healthComponent = world:get(playerEntityId, HealthComponent)
	
	if healthComponent then
		if isEntering then
			-- 進入區域時應用效果
			if zoneConfig.regenerationBonus then
				world:insert(playerEntityId, healthComponent:patch({
					regeneration = healthComponent.regeneration * zoneConfig.regenerationBonus,
				}))
			end
		else
			-- 離開區域時移除效果
			if zoneConfig.regenerationBonus then
				world:insert(playerEntityId, healthComponent:patch({
					regeneration = healthComponent.regeneration / zoneConfig.regenerationBonus,
				}))
			end
		end
	end
	
	-- 處理戰鬥相關效果
	if zoneConfig.type == "safe" then
		-- 安全區：禁止戰鬥
		-- 這裡可以設置戰鬥標記
	elseif zoneConfig.type == "pvp" then
		-- PvP區：允許玩家對戰
		-- 這裡可以設置PvP標記
	end
end

-- 獲取玩家當前區域
function ZoneService:GetPlayerZone(player)
	return playerZones[player]
end

-- 獲取區域配置
function ZoneService:GetZoneConfig(zoneType)
	return ZONE_CONFIG[zoneType]
end

-- 檢查玩家是否可以戰鬥
function ZoneService:CanPlayerCombat(player)
	local zoneType = playerZones[player]
	if not zoneType then return true end -- 不在任何區域時允許戰鬥
	
	local zoneConfig = ZONE_CONFIG[zoneType]
	return zoneConfig and zoneConfig.allowCombat
end

-- 檢查玩家是否可以PvP
function ZoneService:CanPlayerPvP(player)
	local zoneType = playerZones[player]
	if not zoneType then return false end
	
	local zoneConfig = ZONE_CONFIG[zoneType]
	return zoneConfig and zoneConfig.allowPvP
end

-- 獲取區域經驗加成
function ZoneService:GetZoneExpBonus(player)
	local zoneType = playerZones[player]
	if not zoneType then return 1.0 end
	
	local zoneConfig = ZONE_CONFIG[zoneType]
	return zoneConfig and zoneConfig.expBonus or 1.0
end

-- 獲取區域金幣加成
function ZoneService:GetZoneCoinBonus(player)
	local zoneType = playerZones[player]
	if not zoneType then return 1.0 end
	
	local zoneConfig = ZONE_CONFIG[zoneType]
	return zoneConfig and zoneConfig.coinBonus or 1.0
end

-- 創建新區域
function ZoneService:CreateZone(zoneType, zonePart, customConfig)
	if zones[zoneType] then
		warn("Zone already exists:", zoneType)
		return false
	end
	
	local zoneConfig = customConfig or ZONE_CONFIG[zoneType]
	if not zoneConfig then
		warn("Zone config not found:", zoneType)
		return false
	end
	
	local zone = ZonePlus.new(zonePart)
	zones[zoneType] = zone
	
	-- 設置事件監聽
	zone.playerEntered:Connect(function(player)
		self:_onPlayerEnteredZone(player, zoneType, zoneConfig)
	end)
	
	zone.playerExited:Connect(function(player)
		self:_onPlayerExitedZone(player, zoneType, zoneConfig)
	end)
	
	print("🗺️ Zone created:", zoneType)
	return true
end

-- 移除區域
function ZoneService:RemoveZone(zoneType)
	local zone = zones[zoneType]
	if zone then
		zone:destroy()
		zones[zoneType] = nil
		print("🗺️ Zone removed:", zoneType)
		return true
	end
	return false
end

return ZoneService
