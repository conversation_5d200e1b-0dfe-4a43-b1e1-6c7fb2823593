--[[
	PetUIController 測試
	驗證 PetUIController 能正確加載和初始化
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

return function()
	describe("PetUIController", function()
		it("should load without errors", function()
			-- 首先測試 Fusion 是否正確加載
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			expect(Fusion).to.be.ok()
			expect(Fusion.Value).to.be.ok()
			expect(Fusion.Computed).to.be.ok()
			expect(Fusion.New).to.be.ok()
			expect(Fusion.OnEvent).to.be.ok()
			expect(Fusion.Children).to.be.ok()
			
			print("✅ Fusion 0.2 API 驗證通過")
		end)
		
		it("should create Value state objects correctly", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			local Value = Fusion.Value
			
			local testValue = Value("test")
			expect(testValue:get()).to.equal("test")
			
			testValue:set("new value")
			expect(testValue:get()).to.equal("new value")
			
			print("✅ Value 狀態對象測試通過")
		end)
		
		it("should create Computed state objects correctly", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			local Value = Fusion.Value
			local Computed = Fusion.Computed
			
			local baseValue = Value(10)
			local computedValue = Computed(function()
				return baseValue:get() * 2
			end)
			
			expect(computedValue:get()).to.equal(20)
			
			baseValue:set(15)
			expect(computedValue:get()).to.equal(30)
			
			print("✅ Computed 狀態對象測試通過")
		end)
		
		it("should create UI elements with New", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			local New = Fusion.New
			local Children = Fusion.Children
			
			local frame = New "Frame" {
				Name = "TestFrame",
				Size = UDim2.new(0, 100, 0, 100),
				
				[Children] = {
					New "TextLabel" {
						Name = "TestLabel",
						Text = "Hello World",
					},
				},
			}
			
			expect(frame).to.be.ok()
			expect(frame.Name).to.equal("TestFrame")
			expect(frame.Size).to.equal(UDim2.new(0, 100, 0, 100))
			expect(#frame:GetChildren()).to.equal(1)
			expect(frame:FindFirstChild("TestLabel")).to.be.ok()
			
			print("✅ New 和 Children 測試通過")
		end)
	end)
end
