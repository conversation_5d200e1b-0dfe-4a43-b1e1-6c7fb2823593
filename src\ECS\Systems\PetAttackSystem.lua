--[[
	PetAttackSystem.lua - 寵物攻擊系統
	處理寵物自動攻擊目標的邏輯
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local DamageComponent = require(script.Parent.Parent.Components.DamageComponent)
local PetComponent = require(script.Parent.Parent.Components.PetComponent)
local TargetComponent = require(script.Parent.Parent.Components.TargetComponent)
local SwordSwingComponent = require(script.Parent.Parent.Components.SwordSwingComponent)

local function PetAttackSystem(world, deltaTime)
	-- 查詢所有活躍的寵物
	for petId, pet, position, damage in world:query(PetComponent, PositionComponent, DamageComponent) do
		if pet.isActive then
			-- 查找附近的敵人
			local nearestEnemy = nil
			local nearestDistance = math.huge
			local attackRange = 15 -- 寵物攻擊搜索範圍
			
			-- 搜索怪物目標（這裡需要怪物組件，暫時用血量組件代替）
			for enemyId, enemyPos, enemyHealth in world:query(PositionComponent, HealthComponent) do
				if enemyId ~= petId and not world:get(enemyId, PetComponent) then -- 不攻擊其他寵物
					local distance = (enemyPos.position - position.position).Magnitude
					
					if distance < attackRange and distance < nearestDistance and not enemyHealth.isDead then
						nearestEnemy = enemyId
						nearestDistance = distance
					end
				end
			end
			
			-- 如果找到目標
			if nearestEnemy then
				local targetComponent = world:get(petId, TargetComponent)
				local currentTime = tick()
				
				-- 檢查是否需要更新目標或攻擊冷卻
				local shouldAttack = false
				
				if not targetComponent then
					-- 創建目標組件
					world:insert(petId, TargetComponent({
						targetId = nearestEnemy,
						targetType = "Monster",
						lastTargetTime = currentTime,
						attackRange = 8,
						canAttack = true,
						lastAttackTime = 0,
						attackCooldown = 2.0, -- 寵物攻擊冷卻2秒
					}))
					shouldAttack = true
				else
					-- 更新目標
					if targetComponent.targetId ~= nearestEnemy then
						world:insert(petId, targetComponent:patch({
							targetId = nearestEnemy,
							lastTargetTime = currentTime,
						}))
					end
					
					-- 檢查攻擊冷卻
					if (currentTime - targetComponent.lastAttackTime) >= targetComponent.attackCooldown then
						shouldAttack = true
					end
				end
				
				-- 執行攻擊
				if shouldAttack and nearestDistance <= 8 then -- 寵物攻擊範圍8格
					-- 更新攻擊時間
					local updatedTarget = world:get(petId, TargetComponent)
					if updatedTarget then
						world:insert(petId, updatedTarget:patch({
							lastAttackTime = currentTime,
						}))
					end
					
					-- 創建或更新劍擊組件
					local swordSwing = world:get(petId, SwordSwingComponent)
					if not swordSwing then
						world:insert(petId, SwordSwingComponent({
							isSwinging = true,
							swingDuration = 0.4, -- 寵物攻擊較快
							swingStartTime = currentTime,
							range = 8,
							damage = damage.attack,
							weaponId = "PetAttack",
							hasHit = {},
						}))
					else
						world:insert(petId, swordSwing:patch({
							isSwinging = true,
							swingStartTime = currentTime,
							damage = damage.attack,
							hasHit = {},
						}))
					end
					
					print("🐾 Pet", pet.petId, "attacking target", nearestEnemy)
				end
			else
				-- 沒有目標時清除目標組件
				local targetComponent = world:get(petId, TargetComponent)
				if targetComponent then
					world:insert(petId, targetComponent:patch({
						targetId = 0,
						canAttack = false,
					}))
				end
			end
		end
	end
end

return PetAttackSystem
