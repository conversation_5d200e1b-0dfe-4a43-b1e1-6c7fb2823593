--[[
	MonsterEntity.lua - 怪物實體工廠
	創建和管理怪物實體的標準化方法
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local DamageComponent = require(script.Parent.Parent.Components.DamageComponent)
local SwordSwingComponent = require(script.Parent.Parent.Components.SwordSwingComponent)
local TargetComponent = require(script.Parent.Parent.Components.TargetComponent)

local MonsterEntity = {}

-- 怪物組件
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local MonsterComponent = Matter.component("Monster", {
	monsterId = "",
	monsterType = "Basic", -- "Basic", "Elite", "Boss"
	level = 1,
	spawnTime = 0,
	lastAttackTime = 0,
	aggroRange = 20, -- 仇恨範圍
	patrolRange = 15, -- 巡邏範圍
	spawnPosition = Vector3.new(),
	dropTable = {}, -- 掉落表
	expReward = 10,
	coinReward = 5,
})

-- AI 狀態組件（與寵物共用）
local AIStateComponent = require(script.Parent.PetEntity).AIStateComponent

-- 巡邏組件
local PatrolComponent = Matter.component("Patrol", {
	patrolPoints = {}, -- 巡邏點列表
	currentPointIndex = 1,
	patrolSpeed = 8,
	waitTime = 2, -- 在每個巡邏點等待時間
	lastMoveTime = 0,
	isPatrolling = true,
})

-- 怪物配置數據
local MONSTER_CONFIGS = {
	Goblin = {
		name = "哥布林",
		type = "Basic",
		baseStats = {
			health = 60,
			attack = 12,
			defense = 2,
			speed = 10,
		},
		aggroRange = 15,
		patrolRange = 10,
		expReward = 8,
		coinReward = 3,
		dropTable = {
			{item = "Coins", chance = 1.0, amount = {3, 8}},
			{item = "WoodenSword", chance = 0.1, amount = 1},
		},
	},
	
	Orc = {
		name = "獸人",
		type = "Basic",
		baseStats = {
			health = 120,
			attack = 18,
			defense = 5,
			speed = 8,
		},
		aggroRange = 20,
		patrolRange = 12,
		expReward = 15,
		coinReward = 8,
		dropTable = {
			{item = "Coins", chance = 1.0, amount = {5, 12}},
			{item = "IronSword", chance = 0.15, amount = 1},
		},
	},
	
	Troll = {
		name = "巨魔",
		type = "Elite",
		baseStats = {
			health = 250,
			attack = 35,
			defense = 12,
			speed = 6,
		},
		aggroRange = 25,
		patrolRange = 15,
		expReward = 40,
		coinReward = 20,
		dropTable = {
			{item = "Coins", chance = 1.0, amount = {15, 30}},
			{item = "SteelBlade", chance = 0.25, amount = 1},
			{item = "Slime", chance = 0.1, amount = 1}, -- 寵物掉落
		},
	},
	
	DragonBoss = {
		name = "火龍王",
		type = "Boss",
		baseStats = {
			health = 800,
			attack = 80,
			defense = 25,
			speed = 12,
		},
		aggroRange = 40,
		patrolRange = 20,
		expReward = 200,
		coinReward = 100,
		dropTable = {
			{item = "Coins", chance = 1.0, amount = {80, 150}},
			{item = "DragonFang", chance = 0.5, amount = 1},
			{item = "DragonHatchling", chance = 0.3, amount = 1},
			{item = "FireSpirit", chance = 0.2, amount = 1},
		},
	},
}

-- 創建怪物實體
function MonsterEntity.create(world, monsterId, level, spawnPosition, patrolPoints)
	if not world or not monsterId then
		warn("❌ Invalid parameters for MonsterEntity.create")
		return nil
	end
	
	-- 獲取怪物配置
	local config = MONSTER_CONFIGS[monsterId]
	if not config then
		warn("❌ Monster config not found:", monsterId)
		return nil
	end
	
	-- 計算等級調整後的屬性
	level = level or 1
	local levelMultiplier = 1 + (level - 1) * 0.2 -- 每級增加20%屬性
	
	local stats = {
		health = math.floor(config.baseStats.health * levelMultiplier),
		attack = math.floor(config.baseStats.attack * levelMultiplier),
		defense = math.floor(config.baseStats.defense * levelMultiplier),
		speed = config.baseStats.speed,
	}
	
	-- 設置初始位置
	local position = spawnPosition or Vector3.new(0, 10, 0)
	
	-- 創建怪物實體
	local entityId = world:spawn(
		MonsterComponent({
			monsterId = monsterId,
			monsterType = config.type,
			level = level,
			spawnTime = tick(),
			lastAttackTime = 0,
			aggroRange = config.aggroRange,
			patrolRange = config.patrolRange,
			spawnPosition = position,
			dropTable = config.dropTable,
			expReward = math.floor(config.expReward * levelMultiplier),
			coinReward = math.floor(config.coinReward * levelMultiplier),
		}),
		
		PositionComponent({
			position = position,
			rotation = CFrame.new(position),
			lastPosition = position,
			velocity = Vector3.new(),
		}),
		
		HealthComponent({
			current = stats.health,
			maximum = stats.health,
			regeneration = config.type == "Boss" and 2.0 or 0.5, -- Boss 回血較快
			lastDamageTime = 0,
			isDead = false,
		}),
		
		DamageComponent({
			attack = stats.attack,
			defense = stats.defense,
			criticalChance = config.type == "Boss" and 0.15 or 0.05,
			criticalMultiplier = config.type == "Boss" and 2.5 or 1.8,
			elementalType = "None",
			elementalDamage = 0,
		}),
		
		SwordSwingComponent({
			isSwinging = false,
			swingDuration = config.type == "Boss" and 0.6 or 1.0,
			swingStartTime = 0,
			range = config.type == "Boss" and 12 or 8,
			damage = stats.attack,
			weaponId = "MonsterAttack",
			hasHit = {},
		}),
		
		TargetComponent({
			targetId = 0,
			targetType = "Player",
			lastTargetTime = 0,
			attackRange = config.type == "Boss" and 12 or 8,
			canAttack = true,
			lastAttackTime = 0,
			attackCooldown = config.type == "Boss" and 1.0 or 2.5,
		}),
		
		AIStateComponent({
			currentState = "Patrol",
			lastStateChange = tick(),
			stateData = {},
			priority = 1,
		}),
		
		PatrolComponent({
			patrolPoints = patrolPoints or {position},
			currentPointIndex = 1,
			patrolSpeed = stats.speed,
			waitTime = config.type == "Boss" and 1 or 3,
			lastMoveTime = 0,
			isPatrolling = true,
		})
	)
	
	print("👹 Created MonsterEntity:", entityId, "for", monsterId, "level", level)
	return entityId
end

-- 設置怪物 AI 狀態
function MonsterEntity.setAIState(world, entityId, newState, stateData, priority)
	if not world or not entityId then return false end
	
	local aiComponent = world:get(entityId, AIStateComponent)
	if aiComponent then
		-- 檢查優先級
		if priority and priority < aiComponent.priority then
			return false
		end
		
		world:insert(entityId, aiComponent:patch({
			currentState = newState,
			lastStateChange = tick(),
			stateData = stateData or {},
			priority = priority or 1,
		}))
		
		return true
	end
	
	return false
end

-- 設置怪物攻擊目標
function MonsterEntity.setAttackTarget(world, entityId, targetEntityId)
	if not world or not entityId then return false end
	
	local targetComponent = world:get(entityId, TargetComponent)
	if targetComponent then
		world:insert(entityId, targetComponent:patch({
			targetId = targetEntityId or 0,
			lastTargetTime = tick(),
		}))
		
		-- 切換到攻擊狀態
		if targetEntityId then
			MonsterEntity.setAIState(world, entityId, "Attack", {targetId = targetEntityId}, 3)
		else
			MonsterEntity.setAIState(world, entityId, "Patrol", {}, 1)
		end
		
		return true
	end
	
	return false
end

-- 設置巡邏路徑
function MonsterEntity.setPatrolPath(world, entityId, patrolPoints)
	if not world or not entityId or not patrolPoints then return false end
	
	local patrolComponent = world:get(entityId, PatrolComponent)
	if patrolComponent then
		world:insert(entityId, patrolComponent:patch({
			patrolPoints = patrolPoints,
			currentPointIndex = 1,
		}))
		return true
	end
	
	return false
end

-- 獲取怪物數據
function MonsterEntity.getData(world, entityId)
	if not world or not entityId then return nil end
	
	local monsterComponent = world:get(entityId, MonsterComponent)
	local healthComponent = world:get(entityId, HealthComponent)
	local damageComponent = world:get(entityId, DamageComponent)
	local positionComponent = world:get(entityId, PositionComponent)
	local aiComponent = world:get(entityId, AIStateComponent)
	local patrolComponent = world:get(entityId, PatrolComponent)
	
	return {
		monster = monsterComponent,
		health = healthComponent,
		damage = damageComponent,
		position = positionComponent,
		ai = aiComponent,
		patrol = patrolComponent,
	}
end

-- 檢查怪物是否存活
function MonsterEntity.isAlive(world, entityId)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	return healthComponent and not healthComponent.isDead and healthComponent.current > 0
end

-- 處理怪物死亡
function MonsterEntity.handleDeath(world, entityId)
	if not world or not entityId then return nil end
	
	local monsterComponent = world:get(entityId, MonsterComponent)
	if not monsterComponent then return nil end
	
	-- 計算掉落物品
	local drops = {}
	for _, dropData in ipairs(monsterComponent.dropTable) do
		if math.random() <= dropData.chance then
			local amount = dropData.amount
			if type(amount) == "table" then
				amount = math.random(amount[1], amount[2])
			end
			
			table.insert(drops, {
				item = dropData.item,
				amount = amount,
			})
		end
	end
	
	-- 返回獎勵數據
	return {
		exp = monsterComponent.expReward,
		coins = monsterComponent.coinReward,
		drops = drops,
		monsterType = monsterComponent.monsterType,
		level = monsterComponent.level,
	}
end

-- 獲取怪物配置
function MonsterEntity.getConfig(monsterId)
	return MONSTER_CONFIGS[monsterId]
end

-- 獲取所有怪物配置
function MonsterEntity.getAllConfigs()
	return MONSTER_CONFIGS
end

-- 檢查目標是否在仇恨範圍內
function MonsterEntity.isTargetInAggroRange(world, entityId, targetEntityId)
	if not world or not entityId or not targetEntityId then return false end
	
	local monsterPos = world:get(entityId, PositionComponent)
	local targetPos = world:get(targetEntityId, PositionComponent)
	local monsterComponent = world:get(entityId, MonsterComponent)
	
	if monsterPos and targetPos and monsterComponent then
		local distance = (monsterPos.position - targetPos.position).Magnitude
		return distance <= monsterComponent.aggroRange
	end
	
	return false
end

-- 檢查是否在巡邏範圍內
function MonsterEntity.isInPatrolRange(world, entityId, position)
	if not world or not entityId then return false end
	
	local monsterComponent = world:get(entityId, MonsterComponent)
	if monsterComponent then
		local distance = (position - monsterComponent.spawnPosition).Magnitude
		return distance <= monsterComponent.patrolRange
	end
	
	return false
end

-- 導出組件供其他模組使用
MonsterEntity.MonsterComponent = MonsterComponent
MonsterEntity.PatrolComponent = PatrolComponent

return MonsterEntity
