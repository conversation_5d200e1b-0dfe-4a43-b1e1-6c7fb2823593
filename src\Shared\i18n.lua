--[[
	i18n.lua - 國際化/本地化系統
	處理多語言翻譯
]]

local i18n = {}

-- 當前語言
local currentLanguage = "zh-TW"

-- 語言資料
local languages = {}

-- 初始化本地化系統
function i18n.init(defaultLanguage)
	currentLanguage = defaultLanguage or "zh-TW"
	
	-- 載入語言文件
	local LocalizationFolder = script.Parent.Localization
	for _, langFile in pairs(LocalizationFolder:GetChildren()) do
		if langFile:IsA("ModuleScript") then
			local langCode = langFile.Name
			languages[langCode] = require(langFile)
			print("🌐 Loaded language:", langCode)
		end
	end
	
	print("🌐 i18n initialized with language:", currentLanguage)
end

-- 設置當前語言
function i18n.setLanguage(languageCode)
	if languages[languageCode] then
		currentLanguage = languageCode
		print("🌐 Language changed to:", languageCode)
		return true
	else
		warn("🌐 Language not found:", languageCode)
		return false
	end
end

-- 獲取當前語言
function i18n.getCurrentLanguage()
	return currentLanguage
end

-- 翻譯文本
function i18n.translate(key, params)
	local lang = languages[currentLanguage]
	if not lang then
		warn("🌐 Language not loaded:", currentLanguage)
		return key
	end
	
	-- 支援嵌套鍵值 (例如: "ui.buttons.ok")
	local keys = string.split(key, ".")
	local value = lang
	
	for _, k in pairs(keys) do
		if type(value) == "table" and value[k] then
			value = value[k]
		else
			warn("🌐 Translation key not found:", key)
			return key
		end
	end
	
	-- 如果有參數，進行字符串替換
	if params and type(value) == "string" then
		for paramKey, paramValue in pairs(params) do
			value = string.gsub(value, "{" .. paramKey .. "}", tostring(paramValue))
		end
	end
	
	return value
end

-- 簡化的翻譯函數
function i18n.t(key, params)
	return i18n.translate(key, params)
end

-- 獲取可用語言列表
function i18n.getAvailableLanguages()
	local availableLanguages = {}
	for langCode, _ in pairs(languages) do
		table.insert(availableLanguages, langCode)
	end
	return availableLanguages
end

-- 檢查語言是否可用
function i18n.isLanguageAvailable(languageCode)
	return languages[languageCode] ~= nil
end

return i18n
