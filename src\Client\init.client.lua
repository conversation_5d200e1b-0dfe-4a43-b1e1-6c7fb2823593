--[[
	Pet RPG - Client Initialization
	使用 Knit 框架初始化客戶端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

-- 🌐 先初始化本地化系統
local i18n = require(game:GetService("ReplicatedStorage").Shared.i18n)
i18n.init("zh-TW") -- 預設繁體中文
print("🌐 Localization system pre-initialized")

-- 載入所有控制器
local Controllers = script.Controllers
for _, controllerModule in pairs(Controllers:GetChildren()) do
	if controllerModule:IsA("ModuleScript") then
		require(controllerModule)
	end
end

-- 啟動 Knit 客戶端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Client Started!")
	print("🎨 Fusion UI system ready")

	-- 初始化 UI 系統
	local UIController = Knit.GetController("UIController")
	if UIController then
		UIController:InitializeUI()
	end

end):catch(function(err)
	warn("❌ Client startup failed:", err)
end)
