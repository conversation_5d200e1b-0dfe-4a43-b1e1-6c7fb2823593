--[[
	MatterDebugView.lua - Matter ECS 調試視圖
	提供實時的 ECS 系統監控和調試功能
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local MatterDebugView = {}

-- 調試配置
local DEBUG_CONFIG = {
	enabled = false,
	showEntityCount = true,
	showSystemPerformance = true,
	showComponentDetails = true,
	updateInterval = 0.5, -- 更新間隔（秒）
	maxEntitiesShown = 50,
	maxSystemsShown = 20,
}

-- 私有變量
local debugGui = nil
local lastUpdateTime = 0
local performanceData = {}
local entityData = {}

-- 初始化調試視圖
function MatterDebugView.initialize()
	if not _G.MatterWorld then
		warn("❌ Matter World not found! Debug view cannot be initialized.")
		return false
	end
	
	MatterDebugView.createDebugGUI()
	MatterDebugView.setupUpdateLoop()
	
	print("🔍 Matter Debug View initialized")
	return true
end

-- 創建調試 GUI
function MatterDebugView.createDebugGUI()
	local player = Players.LocalPlayer
	if not player then return end
	
	local playerGui = player:WaitForChild("PlayerGui")
	
	-- 移除舊的調試 GUI
	local existingGui = playerGui:FindFirstChild("MatterDebugGUI")
	if existingGui then
		existingGui:Destroy()
	end
	
	-- 創建主 GUI
	debugGui = Instance.new("ScreenGui")
	debugGui.Name = "MatterDebugGUI"
	debugGui.ResetOnSpawn = false
	debugGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
	debugGui.Parent = playerGui
	
	-- 主面板
	local mainFrame = Instance.new("Frame")
	mainFrame.Name = "MainFrame"
	mainFrame.Size = UDim2.new(0, 400, 0, 600)
	mainFrame.Position = UDim2.new(1, -420, 0, 20)
	mainFrame.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
	mainFrame.BackgroundTransparency = 0.1
	mainFrame.BorderSizePixel = 0
	mainFrame.Parent = debugGui
	
	-- 圓角
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = mainFrame
	
	-- 標題欄
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 30)
	titleBar.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = mainFrame
	
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 8)
	titleCorner.Parent = titleBar
	
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Size = UDim2.new(1, -60, 1, 0)
	titleLabel.Position = UDim2.new(0, 10, 0, 0)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Text = "Matter ECS Debug"
	titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	titleLabel.TextScaled = true
	titleLabel.Font = Enum.Font.GothamBold
	titleLabel.TextXAlignment = Enum.TextXAlignment.Left
	titleLabel.Parent = titleBar
	
	-- 關閉按鈕
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 25, 0, 25)
	closeButton.Position = UDim2.new(1, -30, 0, 2.5)
	closeButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	closeButton.BorderSizePixel = 0
	closeButton.Text = "✕"
	closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.GothamBold
	closeButton.Parent = titleBar
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 4)
	closeCorner.Parent = closeButton
	
	closeButton.Activated:Connect(function()
		MatterDebugView.toggle()
	end)
	
	-- 滾動框
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Name = "ScrollFrame"
	scrollFrame.Size = UDim2.new(1, -10, 1, -40)
	scrollFrame.Position = UDim2.new(0, 5, 0, 35)
	scrollFrame.BackgroundTransparency = 1
	scrollFrame.ScrollBarThickness = 8
	scrollFrame.ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100)
	scrollFrame.Parent = mainFrame
	
	-- 內容容器
	local contentFrame = Instance.new("Frame")
	contentFrame.Name = "ContentFrame"
	contentFrame.Size = UDim2.new(1, -10, 0, 0)
	contentFrame.BackgroundTransparency = 1
	contentFrame.Parent = scrollFrame
	
	-- 自動布局
	local listLayout = Instance.new("UIListLayout")
	listLayout.SortOrder = Enum.SortOrder.LayoutOrder
	listLayout.Padding = UDim.new(0, 5)
	listLayout.Parent = contentFrame
	
	-- 更新內容大小
	listLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
		contentFrame.Size = UDim2.new(1, -10, 0, listLayout.AbsoluteContentSize.Y)
		scrollFrame.CanvasSize = UDim2.new(0, 0, 0, listLayout.AbsoluteContentSize.Y + 10)
	end)
	
	-- 使面板可拖拽
	MatterDebugView.makeDraggable(mainFrame, titleBar)
end

-- 使面板可拖拽
function MatterDebugView.makeDraggable(frame, dragHandle)
	local dragging = false
	local dragStart = nil
	local startPos = nil
	
	dragHandle.InputBegan:Connect(function(input)
		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			dragging = true
			dragStart = input.Position
			startPos = frame.Position
		end
	end)
	
	dragHandle.InputChanged:Connect(function(input)
		if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
			local delta = input.Position - dragStart
			frame.Position = UDim2.new(
				startPos.X.Scale,
				startPos.X.Offset + delta.X,
				startPos.Y.Scale,
				startPos.Y.Offset + delta.Y
			)
		end
	end)
	
	dragHandle.InputEnded:Connect(function(input)
		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			dragging = false
		end
	end)
end

-- 設置更新循環
function MatterDebugView.setupUpdateLoop()
	RunService.Heartbeat:Connect(function()
		if DEBUG_CONFIG.enabled and debugGui and debugGui.Parent then
			local currentTime = tick()
			if currentTime - lastUpdateTime >= DEBUG_CONFIG.updateInterval then
				MatterDebugView.updateDebugInfo()
				lastUpdateTime = currentTime
			end
		end
	end)
end

-- 更新調試信息
function MatterDebugView.updateDebugInfo()
	if not debugGui or not _G.MatterWorld then return end
	
	local contentFrame = debugGui.MainFrame.ScrollFrame.ContentFrame
	
	-- 清除舊內容
	for _, child in ipairs(contentFrame:GetChildren()) do
		if child:IsA("Frame") then
			child:Destroy()
		end
	end
	
	local layoutOrder = 1
	
	-- 實體統計
	if DEBUG_CONFIG.showEntityCount then
		MatterDebugView.createEntityCountSection(contentFrame, layoutOrder)
		layoutOrder = layoutOrder + 1
	end
	
	-- 系統性能
	if DEBUG_CONFIG.showSystemPerformance then
		MatterDebugView.createSystemPerformanceSection(contentFrame, layoutOrder)
		layoutOrder = layoutOrder + 1
	end
	
	-- 組件詳情
	if DEBUG_CONFIG.showComponentDetails then
		MatterDebugView.createComponentDetailsSection(contentFrame, layoutOrder)
		layoutOrder = layoutOrder + 1
	end
end

-- 創建實體計數部分
function MatterDebugView.createEntityCountSection(parent, layoutOrder)
	local section = MatterDebugView.createSection(parent, "實體統計", layoutOrder)
	
	-- 統計實體數量
	local entityCount = 0
	local componentCounts = {}
	
	for entityId in _G.MatterWorld:query() do
		entityCount = entityCount + 1
		
		-- 統計組件
		for componentType, component in _G.MatterWorld:get(entityId) do
			local componentName = tostring(componentType)
			componentCounts[componentName] = (componentCounts[componentName] or 0) + 1
		end
	end
	
	-- 顯示總實體數
	MatterDebugView.createInfoLabel(section, "總實體數: " .. entityCount, Color3.fromRGB(100, 255, 100))
	
	-- 顯示組件統計
	for componentName, count in pairs(componentCounts) do
		MatterDebugView.createInfoLabel(section, componentName .. ": " .. count, Color3.fromRGB(200, 200, 255))
	end
end

-- 創建系統性能部分
function MatterDebugView.createSystemPerformanceSection(parent, layoutOrder)
	local section = MatterDebugView.createSection(parent, "系統性能", layoutOrder)
	
	-- 這裡可以添加系統性能監控
	-- 由於 Matter 沒有內建的性能監控，我們可以添加自定義的監控
	
	MatterDebugView.createInfoLabel(section, "FPS: " .. math.floor(1 / RunService.Heartbeat:Wait()), Color3.fromRGB(255, 255, 100))
	MatterDebugView.createInfoLabel(section, "記憶體使用: " .. math.floor(collectgarbage("count")) .. " KB", Color3.fromRGB(255, 200, 100))
end

-- 創建組件詳情部分
function MatterDebugView.createComponentDetailsSection(parent, layoutOrder)
	local section = MatterDebugView.createSection(parent, "實體詳情", layoutOrder)
	
	local entityCount = 0
	for entityId in _G.MatterWorld:query() do
		entityCount = entityCount + 1
		if entityCount <= DEBUG_CONFIG.maxEntitiesShown then
			local entityInfo = "實體 " .. entityId .. ": "
			local components = {}
			
			for componentType, component in _G.MatterWorld:get(entityId) do
				table.insert(components, tostring(componentType))
			end
			
			entityInfo = entityInfo .. table.concat(components, ", ")
			MatterDebugView.createInfoLabel(section, entityInfo, Color3.fromRGB(200, 200, 200))
		end
	end
	
	if entityCount > DEBUG_CONFIG.maxEntitiesShown then
		MatterDebugView.createInfoLabel(section, "... 還有 " .. (entityCount - DEBUG_CONFIG.maxEntitiesShown) .. " 個實體", Color3.fromRGB(150, 150, 150))
	end
end

-- 創建部分容器
function MatterDebugView.createSection(parent, title, layoutOrder)
	local section = Instance.new("Frame")
	section.Name = title
	section.Size = UDim2.new(1, 0, 0, 0)
	section.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	section.BorderSizePixel = 0
	section.LayoutOrder = layoutOrder
	section.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = section
	
	-- 標題
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Size = UDim2.new(1, -10, 0, 25)
	titleLabel.Position = UDim2.new(0, 5, 0, 5)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Text = title
	titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	titleLabel.TextScaled = true
	titleLabel.Font = Enum.Font.GothamBold
	titleLabel.TextXAlignment = Enum.TextXAlignment.Left
	titleLabel.Parent = section
	
	-- 內容容器
	local contentContainer = Instance.new("Frame")
	contentContainer.Name = "Content"
	contentContainer.Size = UDim2.new(1, -10, 0, 0)
	contentContainer.Position = UDim2.new(0, 5, 0, 30)
	contentContainer.BackgroundTransparency = 1
	contentContainer.Parent = section
	
	local listLayout = Instance.new("UIListLayout")
	listLayout.SortOrder = Enum.SortOrder.LayoutOrder
	listLayout.Padding = UDim.new(0, 2)
	listLayout.Parent = contentContainer
	
	-- 自動調整大小
	listLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
		contentContainer.Size = UDim2.new(1, -10, 0, listLayout.AbsoluteContentSize.Y)
		section.Size = UDim2.new(1, 0, 0, listLayout.AbsoluteContentSize.Y + 40)
	end)
	
	return contentContainer
end

-- 創建信息標籤
function MatterDebugView.createInfoLabel(parent, text, textColor)
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(1, 0, 0, 20)
	label.BackgroundTransparency = 1
	label.Text = text
	label.TextColor3 = textColor or Color3.fromRGB(255, 255, 255)
	label.TextScaled = true
	label.Font = Enum.Font.Gotham
	label.TextXAlignment = Enum.TextXAlignment.Left
	label.Parent = parent
	
	return label
end

-- 切換調試視圖
function MatterDebugView.toggle()
	DEBUG_CONFIG.enabled = not DEBUG_CONFIG.enabled
	
	if debugGui then
		debugGui.Enabled = DEBUG_CONFIG.enabled
	end
	
	print("🔍 Matter Debug View:", DEBUG_CONFIG.enabled and "Enabled" or "Disabled")
end

-- 設置配置
function MatterDebugView.setConfig(config)
	for key, value in pairs(config) do
		if DEBUG_CONFIG[key] ~= nil then
			DEBUG_CONFIG[key] = value
		end
	end
end

-- 獲取配置
function MatterDebugView.getConfig()
	return DEBUG_CONFIG
end

-- 銷毀調試視圖
function MatterDebugView.destroy()
	if debugGui then
		debugGui:Destroy()
		debugGui = nil
	end
	
	DEBUG_CONFIG.enabled = false
	print("🔍 Matter Debug View destroyed")
end

return MatterDebugView
