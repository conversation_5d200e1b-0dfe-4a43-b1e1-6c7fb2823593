--[[
	MonsterAISystem.lua - 怪物 AI 系統
	處理怪物的巡邏、追擊、攻擊邏輯
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local DamageComponent = require(script.Parent.Parent.Components.DamageComponent)
local SwordSwingComponent = require(script.Parent.Parent.Components.SwordSwingComponent)
local TargetComponent = require(script.Parent.Parent.Components.TargetComponent)

-- 實體模組
local MonsterEntity = require(script.Parent.Parent.Entities.MonsterEntity)
local PlayerEntity = require(script.Parent.Parent.Entities.PlayerEntity)
local PetEntity = require(script.Parent.Parent.Entities.PetEntity)

-- 組件
local MonsterComponent = MonsterEntity.MonsterComponent
local PatrolComponent = MonsterEntity.PatrolComponent
local AIStateComponent = PetEntity.AIStateComponent

local function MonsterAISystem(world, deltaTime)
	-- 查詢所有怪物實體
	for monsterId, monster, position, health, ai in world:query(MonsterComponent, PositionComponent, HealthComponent, AIStateComponent) do
		if not health.isDead then
			-- 根據 AI 狀態執行不同邏輯
			if ai.currentState == "Patrol" then
				handlePatrolState(world, monsterId, monster, position, ai, deltaTime)
			elseif ai.currentState == "Chase" then
				handleChaseState(world, monsterId, monster, position, ai, deltaTime)
			elseif ai.currentState == "Attack" then
				handleAttackState(world, monsterId, monster, position, ai, deltaTime)
			elseif ai.currentState == "Return" then
				handleReturnState(world, monsterId, monster, position, ai, deltaTime)
			end
			
			-- 檢查是否有新的目標進入仇恨範圍
			if ai.currentState == "Patrol" or ai.currentState == "Return" then
				checkForTargets(world, monsterId, monster, position)
			end
		end
	end
end

-- 處理巡邏狀態
function handlePatrolState(world, entityId, monster, position, ai, deltaTime)
	local patrol = world:get(entityId, PatrolComponent)
	if not patrol or not patrol.isPatrolling then return end
	
	local currentTime = tick()
	
	-- 檢查是否到達當前巡邏點
	local currentPoint = patrol.patrolPoints[patrol.currentPointIndex]
	if currentPoint then
		local distance = (position.position - currentPoint).Magnitude
		
		if distance <= 3 then
			-- 到達巡邏點，等待一段時間後前往下一個點
			if currentTime - patrol.lastMoveTime >= patrol.waitTime then
				local nextIndex = patrol.currentPointIndex + 1
				if nextIndex > #patrol.patrolPoints then
					nextIndex = 1
				end
				
				world:insert(entityId, patrol:patch({
					currentPointIndex = nextIndex,
					lastMoveTime = currentTime,
				}))
			end
		else
			-- 移動到當前巡邏點
			local direction = (currentPoint - position.position).Unit
			local newPosition = position.position + direction * patrol.patrolSpeed * deltaTime
			
			-- 更新位置
			world:insert(entityId, position:patch({
				lastPosition = position.position,
				position = newPosition,
				rotation = CFrame.lookAt(newPosition, newPosition + direction),
				velocity = direction * patrol.patrolSpeed,
			}))
			
			-- 在 Roblox 中更新模型位置
			updateMonsterModel(entityId, newPosition, direction)
		end
	end
end

-- 處理追擊狀態
function handleChaseState(world, entityId, monster, position, ai, deltaTime)
	local targetId = ai.stateData.targetId
	if not targetId then
		-- 沒有目標，回到巡邏狀態
		MonsterEntity.setAIState(world, entityId, "Patrol", {}, 1)
		return
	end
	
	-- 獲取目標位置
	local targetPosition = world:get(targetId, PositionComponent)
	if not targetPosition then
		-- 目標不存在，回到巡邏狀態
		MonsterEntity.setAIState(world, entityId, "Patrol", {}, 1)
		return
	end
	
	local distance = (targetPosition.position - position.position).Magnitude
	
	-- 檢查是否超出仇恨範圍
	if distance > monster.aggroRange * 1.5 then -- 給一些緩衝距離
		-- 超出範圍，返回出生點
		MonsterEntity.setAIState(world, entityId, "Return", {}, 2)
		return
	end
	
	-- 檢查是否進入攻擊範圍
	local targetComponent = world:get(entityId, TargetComponent)
	if targetComponent and distance <= targetComponent.attackRange then
		-- 進入攻擊範圍，切換到攻擊狀態
		MonsterEntity.setAIState(world, entityId, "Attack", {targetId = targetId}, 3)
		return
	end
	
	-- 移動向目標
	local direction = (targetPosition.position - position.position).Unit
	local moveSpeed = 12 -- 追擊時移動較快
	local newPosition = position.position + direction * moveSpeed * deltaTime
	
	-- 更新位置
	world:insert(entityId, position:patch({
		lastPosition = position.position,
		position = newPosition,
		rotation = CFrame.lookAt(newPosition, newPosition + direction),
		velocity = direction * moveSpeed,
	}))
	
	-- 在 Roblox 中更新模型位置
	updateMonsterModel(entityId, newPosition, direction)
end

-- 處理攻擊狀態
function handleAttackState(world, entityId, monster, position, ai, deltaTime)
	local targetId = ai.stateData.targetId
	if not targetId then
		-- 沒有目標，回到巡邏狀態
		MonsterEntity.setAIState(world, entityId, "Patrol", {}, 1)
		return
	end
	
	-- 獲取目標位置
	local targetPosition = world:get(targetId, PositionComponent)
	local targetHealth = world:get(targetId, HealthComponent)
	
	if not targetPosition or not targetHealth or targetHealth.isDead then
		-- 目標死亡或不存在，回到巡邏狀態
		MonsterEntity.setAIState(world, entityId, "Patrol", {}, 1)
		return
	end
	
	local distance = (targetPosition.position - position.position).Magnitude
	local targetComponent = world:get(entityId, TargetComponent)
	
	if not targetComponent then return end
	
	-- 檢查是否超出攻擊範圍
	if distance > targetComponent.attackRange * 1.2 then -- 給一些緩衝距離
		-- 超出攻擊範圍，切換到追擊狀態
		MonsterEntity.setAIState(world, entityId, "Chase", {targetId = targetId}, 2)
		return
	end
	
	-- 檢查攻擊冷卻
	local currentTime = tick()
	if currentTime - targetComponent.lastAttackTime >= targetComponent.attackCooldown then
		-- 執行攻擊
		performMonsterAttack(world, entityId, targetId, currentTime)
	end
end

-- 處理返回狀態
function handleReturnState(world, entityId, monster, position, ai, deltaTime)
	local spawnPosition = monster.spawnPosition
	local distance = (spawnPosition - position.position).Magnitude
	
	if distance <= 5 then
		-- 回到出生點，切換到巡邏狀態
		MonsterEntity.setAIState(world, entityId, "Patrol", {}, 1)
		
		-- 回血
		local health = world:get(entityId, HealthComponent)
		if health then
			world:insert(entityId, health:patch({
				current = health.maximum,
			}))
		end
		
		return
	end
	
	-- 移動向出生點
	local direction = (spawnPosition - position.position).Unit
	local moveSpeed = 10
	local newPosition = position.position + direction * moveSpeed * deltaTime
	
	-- 更新位置
	world:insert(entityId, position:patch({
		lastPosition = position.position,
		position = newPosition,
		rotation = CFrame.lookAt(newPosition, newPosition + direction),
		velocity = direction * moveSpeed,
	}))
	
	-- 在 Roblox 中更新模型位置
	updateMonsterModel(entityId, newPosition, direction)
end

-- 檢查目標
function checkForTargets(world, entityId, monster, position)
	local nearestTarget = nil
	local nearestDistance = math.huge
	
	-- 搜索玩家目標
	for playerId, playerPos, playerHealth in world:query(PositionComponent, HealthComponent) do
		-- 檢查是否是玩家實體（有 PlayerComponent）
		local playerComponent = world:get(playerId, PlayerEntity.PlayerComponent)
		if playerComponent and not playerHealth.isDead then
			local distance = (playerPos.position - position.position).Magnitude
			
			if distance <= monster.aggroRange and distance < nearestDistance then
				nearestTarget = playerId
				nearestDistance = distance
			end
		end
	end
	
	-- 搜索寵物目標
	for petId, petPos, petHealth in world:query(PositionComponent, HealthComponent) do
		-- 檢查是否是寵物實體（有 PetComponent）
		local petComponent = world:get(petId, require(script.Parent.Parent.Components.PetComponent))
		if petComponent and petComponent.isActive and not petHealth.isDead then
			local distance = (petPos.position - position.position).Magnitude
			
			if distance <= monster.aggroRange and distance < nearestDistance then
				nearestTarget = petId
				nearestDistance = distance
			end
		end
	end
	
	-- 如果找到目標，切換到追擊狀態
	if nearestTarget then
		MonsterEntity.setAIState(world, entityId, "Chase", {targetId = nearestTarget}, 2)
		MonsterEntity.setAttackTarget(world, entityId, nearestTarget)
		print("👹 Monster", entityId, "found target:", nearestTarget)
	end
end

-- 執行怪物攻擊
function performMonsterAttack(world, entityId, targetId, currentTime)
	local damage = world:get(entityId, DamageComponent)
	if not damage then return end
	
	-- 更新攻擊時間
	local targetComponent = world:get(entityId, TargetComponent)
	if targetComponent then
		world:insert(entityId, targetComponent:patch({
			lastAttackTime = currentTime,
		}))
	end
	
	-- 創建或更新劍擊組件
	local swordSwing = world:get(entityId, SwordSwingComponent)
	if not swordSwing then
		world:insert(entityId, SwordSwingComponent({
			isSwinging = true,
			swingDuration = 0.6,
			swingStartTime = currentTime,
			range = 8,
			damage = damage.attack,
			weaponId = "MonsterAttack",
			hasHit = {},
		}))
	else
		world:insert(entityId, swordSwing:patch({
			isSwinging = true,
			swingStartTime = currentTime,
			damage = damage.attack,
			hasHit = {},
		}))
	end
	
	print("👹 Monster", entityId, "attacking target", targetId)
end

-- 更新怪物模型位置
function updateMonsterModel(entityId, newPosition, direction)
	local monsterModel = workspace:FindFirstChild("Monster_" .. entityId)
	if monsterModel and monsterModel.PrimaryPart then
		local newCFrame = CFrame.lookAt(newPosition, newPosition + direction)
		monsterModel:SetPrimaryPartCFrame(newCFrame)
	end
end

return MonsterAISystem
