--[[
	PetSystemDiagnosis.lua - 寵物系統診斷
	檢查寵物召喚系統的所有組件
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🔍 Pet System Diagnosis Starting...")
print("=" .. string.rep("=", 60))

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待服務初始化
	task.wait(2)
	
	print("\n📋 SYSTEM COMPONENT CHECK")
	print(string.rep("-", 40))
	
	-- 1. 檢查 Matter World
	local worldStatus = _G.MatterWorld and "✅ Available" or "❌ Missing"
	print("Matter World:", worldStatus)
	
	if _G.MatterWorld then
		local entityCount = 0
		for _ in _G.MatterWorld:query() do
			entityCount = entityCount + 1
		end
		print("  Entities:", entityCount)
	end
	
	-- 2. 檢查服務
	local PetService = Knit.GetService("PetService")
	local petServiceStatus = PetService and "✅ Available" or "❌ Missing"
	print("PetService:", petServiceStatus)
	
	-- 3. 檢查 PlayerProfile
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	local profileStatus = profile and "✅ Available" or "❌ Missing"
	print("Player Profile:", profileStatus)
	
	if profile then
		print("  Player:", player.Name)
		print("  Level:", profile.Data.level)
		print("  Coins:", profile.Data.coins)
		print("  Owned pets:", profile.Data.ownedPets)
	end
	
	-- 4. 檢查 PetDatabase
	local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.PetDatabase)
	local slimePet = PetDatabase.getPet("Slime")
	local databaseStatus = slimePet and "✅ Available" or "❌ Missing"
	print("Pet Database:", databaseStatus)
	
	if slimePet then
		print("  Slime config:", slimePet.name)
	end
	
	-- 5. 檢查 ECS 組件
	print("\nECS Components:")
	local components = {
		"PositionComponent",
		"HealthComponent", 
		"DamageComponent",
		"PetComponent",
		"FollowTargetComponent"
	}
	
	for _, componentName in ipairs(components) do
		local success, component = pcall(function()
			return require(game:GetService("ReplicatedStorage").ECS.Components[componentName])
		end)
		local status = success and "✅" or "❌"
		print("  " .. componentName .. ":", status)
	end
	
	-- 6. 檢查 ECS 實體
	print("\nECS Entities:")
	local entities = {"PlayerEntity", "PetEntity", "MonsterEntity"}
	
	for _, entityName in ipairs(entities) do
		local success, entity = pcall(function()
			return require(game:GetService("ReplicatedStorage").ECS.Entities[entityName])
		end)
		local status = success and "✅" or "❌"
		print("  " .. entityName .. ":", status)
	end
	
	-- 7. 檢查 ECS 系統
	print("\nECS Systems:")
	local systems = {
		"PetFollowSystem",
		"PetAttackSystem", 
		"HealthSystem",
		"SwordAttackSystem",
		"MonsterAISystem",
		"LifetimeSystem"
	}
	
	for _, systemName in ipairs(systems) do
		local success, system = pcall(function()
			return require(game:GetService("ReplicatedStorage").ECS.Systems[systemName])
		end)
		local status = success and "✅" or "❌"
		print("  " .. systemName .. ":", status)
	end
	
	print("\n" .. string.rep("-", 40))
	print("🧪 FUNCTIONAL TEST")
	print(string.rep("-", 40))
	
	if not PetService then
		print("❌ Cannot test - PetService missing")
		return
	end
	
	if not profile then
		print("❌ Cannot test - Player profile missing")
		return
	end
	
	-- 確保玩家有史萊姆寵物
	if not profile.Data.ownedPets.Slime then
		print("💰 Adding Slime pet for testing...")
		PlayerProfile.addPet(player, "Slime", {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
		print("  Slime pet added")
	end
	
	-- 監聽召喚事件
	local testResults = {
		summonSignal = false,
		modelCreated = false,
		entityCreated = false,
	}
	
	PetService.PetSummoned:Connect(function(petId)
		testResults.summonSignal = true
		print("✅ Summon signal received:", petId)
		
		-- 檢查模型
		task.spawn(function()
			for i = 1, 10 do
				for _, child in ipairs(workspace:GetChildren()) do
					if child.Name:match("Pet_") then
						testResults.modelCreated = true
						print("✅ Pet model created:", child.Name)
						return
					end
				end
				task.wait(0.5)
			end
		end)
		
		-- 檢查實體
		if _G.MatterWorld then
			task.spawn(function()
				task.wait(1)
				local entityFound = false
				for entityId, pet in _G.MatterWorld:query(require(game:GetService("ReplicatedStorage").ECS.Components.PetComponent)) do
					if pet.petId == petId then
						testResults.entityCreated = true
						entityFound = true
						print("✅ Pet entity created:", entityId)
						break
					end
				end
				if not entityFound then
					print("❌ Pet entity not found in ECS")
				end
			end)
		end
	end)
	
	-- 執行召喚測試
	print("\n🧪 Testing pet summon...")
	PetService.SummonPet:Fire("Slime")
	
	-- 等待結果
	task.wait(5)
	
	print("\n" .. string.rep("=", 60))
	print("📊 DIAGNOSIS RESULTS")
	print(string.rep("=", 60))
	
	print("Summon Signal:", testResults.summonSignal and "✅ PASS" or "❌ FAIL")
	print("Model Created:", testResults.modelCreated and "✅ PASS" or "❌ FAIL")
	print("Entity Created:", testResults.entityCreated and "✅ PASS" or "❌ FAIL")
	
	-- 總結問題
	print("\n🔧 RECOMMENDATIONS:")
	
	if not _G.MatterWorld then
		print("❌ Fix: Matter World not initialized")
	end
	
	if not testResults.summonSignal then
		print("❌ Fix: PetService summon logic not working")
		print("   - Check PetService:_summonPet method")
		print("   - Check player profile and pet ownership")
	end
	
	if testResults.summonSignal and not testResults.modelCreated then
		print("❌ Fix: Pet model creation failed")
		print("   - Check PetService:_createPetModel method")
		print("   - Check workspace permissions")
	end
	
	if testResults.summonSignal and not testResults.entityCreated then
		print("❌ Fix: ECS entity creation failed")
		print("   - Check PetEntity.create method")
		print("   - Check Matter World connection")
	end
	
	if testResults.summonSignal and testResults.modelCreated and testResults.entityCreated then
		print("✅ Pet summoning system is working!")
		print("   If pets are not following, check:")
		print("   - PetFollowSystem is running")
		print("   - ECS main loop is active")
		print("   - Player entity exists in ECS")
	end
	
	print(string.rep("=", 60))
	
end):catch(function(err)
	warn("❌ Diagnosis failed with error:", err)
end)

return true
