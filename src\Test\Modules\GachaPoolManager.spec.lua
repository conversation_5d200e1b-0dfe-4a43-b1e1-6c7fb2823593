--[[
	GachaPoolManager.spec.lua - GachaPoolManager 單元測試
]]

return function()
	local GachaPoolManager = require(game:GetService("ReplicatedStorage").Shared.Modules.GachaPoolManager)
	
	describe("GachaPoolManager", function()
		beforeEach(function()
			-- 初始化抽卡池管理器
			GachaPoolManager.initialize()
		end)
		
		afterEach(function()
			-- 清理（如果需要的話）
		end)
		
		describe("池子管理", function()
			it("應該正確初始化預設池", function()
				local availablePools = GachaPoolManager.getAvailablePools()
				
				expect(availablePools).to.be.ok()
				expect(type(availablePools)).to.equal("table")
				
				-- 檢查是否有標準池
				expect(availablePools.standard_pet).to.be.ok()
				expect(availablePools.standard_weapon).to.be.ok()
				expect(availablePools.mixed_pool).to.be.ok()
			end)
			
			it("應該能夠獲取指定池的配置", function()
				local petPool = GachaPoolManager.getPool("standard_pet")
				
				expect(petPool).to.be.ok()
				expect(petPool.id).to.equal("standard_pet")
				expect(petPool.type).to.equal("pet")
				expect(petPool.isActive).to.equal(true)
				expect(petPool.rates).to.be.ok()
				expect(petPool.items).to.be.ok()
			end)
			
			it("應該能夠創建自定義池", function()
				local customPool = {
					id = "test_pool",
					name = "測試池",
					type = "pet",
					isActive = true,
					isLimited = false,
					rates = {
						Common = 0.8,
						Uncommon = 0.2,
					},
					items = {
						Common = {"TestPet1"},
						Uncommon = {"TestPet2"},
					},
				}
				
				local success = GachaPoolManager.createCustomPool(customPool)
				expect(success).to.equal(true)
				
				local retrievedPool = GachaPoolManager.getPool("test_pool")
				expect(retrievedPool).to.be.ok()
				expect(retrievedPool.name).to.equal("測試池")
			end)
			
			it("應該能夠移除池", function()
				-- 先創建一個測試池
				local testPool = {
					id = "removable_pool",
					name = "可移除池",
					type = "pet",
					isActive = true,
				}
				
				GachaPoolManager.createCustomPool(testPool)
				expect(GachaPoolManager.getPool("removable_pool")).to.be.ok()
				
				-- 移除池
				local success = GachaPoolManager.removePool("removable_pool")
				expect(success).to.equal(true)
				expect(GachaPoolManager.getPool("removable_pool")).to.equal(nil)
			end)
		end)
		
		describe("物品選擇", function()
			it("應該能夠從寵物池選擇物品", function()
				local selectedItem = GachaPoolManager.selectFromPool("standard_pet", "Common", {})
				
				expect(selectedItem).to.be.ok()
				expect(selectedItem.type).to.equal("pet")
				expect(selectedItem.rarity).to.equal("Common")
				expect(selectedItem.poolId).to.equal("standard_pet")
				expect(selectedItem.id).to.be.ok()
			end)
			
			it("應該能夠從武器池選擇物品", function()
				local selectedItem = GachaPoolManager.selectFromPool("standard_weapon", "Uncommon", {})
				
				expect(selectedItem).to.be.ok()
				expect(selectedItem.type).to.equal("weapon")
				expect(selectedItem.rarity).to.equal("Uncommon")
				expect(selectedItem.poolId).to.equal("standard_weapon")
			end)
			
			it("應該能夠從混合池選擇物品", function()
				local selectedItem = GachaPoolManager.selectFromPool("mixed_pool", "Rare", {})
				
				expect(selectedItem).to.be.ok()
				expect(selectedItem.rarity).to.equal("Rare")
				expect(selectedItem.poolId).to.equal("mixed_pool")
				-- 混合池應該返回寵物或武器
				expect(selectedItem.type == "pet" or selectedItem.type == "weapon").to.equal(true)
			end)
			
			it("應該處理無效的池ID", function()
				local selectedItem = GachaPoolManager.selectFromPool("invalid_pool", "Common", {})
				expect(selectedItem).to.equal(nil)
			end)
			
			it("應該處理無效的稀有度", function()
				local selectedItem = GachaPoolManager.selectFromPool("standard_pet", "InvalidRarity", {})
				expect(selectedItem).to.equal(nil)
			end)
		end)
		
		describe("保底機制", function()
			it("應該正確檢查保底", function()
				local pityData = {
					rare = 9,
					epic = 45,
					legendary = 95,
				}
				
				-- 稀有保底
				local guaranteedRarity = GachaPoolManager.checkPity("standard_pet", {rare = 10})
				expect(guaranteedRarity).to.equal("Rare")
				
				-- 傳說保底
				guaranteedRarity = GachaPoolManager.checkPity("standard_pet", {epic = 50})
				expect(guaranteedRarity).to.equal("Epic")
				
				-- 神話保底
				guaranteedRarity = GachaPoolManager.checkPity("standard_pet", {legendary = 100})
				expect(guaranteedRarity).to.equal("Legendary")
			end)
			
			it("應該在未達到保底時返回nil", function()
				local pityData = {
					rare = 5,
					epic = 20,
					legendary = 30,
				}
				
				local guaranteedRarity = GachaPoolManager.checkPity("standard_pet", pityData)
				expect(guaranteedRarity).to.equal(nil)
			end)
		end)
		
		describe("特殊規則", function()
			it("應該處理十連保底稀有", function()
				local guaranteedRarity = GachaPoolManager.applySpecialRules("standard_pet", 10, false)
				expect(guaranteedRarity).to.equal("Rare")
			end)
			
			it("應該處理首次十連獎勵", function()
				-- 創建有首次獎勵的測試池
				local testPool = {
					id = "first_time_pool",
					name = "首次獎勵池",
					type = "pet",
					isActive = true,
					rules = {
						firstTimeBonus = true,
						guaranteedRareIn10 = true,
					},
				}
				
				GachaPoolManager.createCustomPool(testPool)
				
				local guaranteedRarity = GachaPoolManager.applySpecialRules("first_time_pool", 10, true)
				expect(guaranteedRarity).to.equal("Epic")
			end)
			
			it("應該在非十連時不觸發保底", function()
				local guaranteedRarity = GachaPoolManager.applySpecialRules("standard_pet", 5, false)
				expect(guaranteedRarity).to.equal(nil)
			end)
		end)
		
		describe("池子統計", function()
			it("應該正確計算池子統計信息", function()
				local stats = GachaPoolManager.getPoolStats("standard_pet")
				
				expect(stats).to.be.ok()
				expect(stats.totalItems).to.be.greaterThan(0)
				expect(stats.itemsByRarity).to.be.ok()
				expect(stats.itemsByRarity.Common).to.be.greaterThan(0)
			end)
			
			it("應該處理混合池的統計", function()
				local stats = GachaPoolManager.getPoolStats("mixed_pool")
				
				expect(stats).to.be.ok()
				expect(stats.totalItems).to.be.greaterThan(0)
				expect(stats.itemsByRarity).to.be.ok()
			end)
			
			it("應該處理無效池的統計", function()
				local stats = GachaPoolManager.getPoolStats("invalid_pool")
				expect(stats).to.equal(nil)
			end)
		end)
		
		describe("限時池管理", function()
			it("應該能夠更新限時池狀態", function()
				-- 這個測試需要模擬時間，在實際環境中可能需要更複雜的設置
				expect(function()
					GachaPoolManager.updateLimitedPools()
				end).never.to.throw()
			end)
		end)
		
		describe("錯誤處理", function()
			it("應該處理缺少ID的自定義池", function()
				local invalidPool = {
					name = "無ID池",
					type = "pet",
				}
				
				local success = GachaPoolManager.createCustomPool(invalidPool)
				expect(success).to.equal(false)
			end)
			
			it("應該處理移除不存在的池", function()
				local success = GachaPoolManager.removePool("nonexistent_pool")
				expect(success).to.equal(false)
			end)
		end)
	end)
end
