--[[
	PlayerService.lua - 玩家管理服務（重構版）
	整合 Matter ECS 和 ProfileService 的玩家管理
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

-- ECS 組件
local PositionComponent = require(game:GetService("ReplicatedStorage").ECS.Components.PositionComponent)
local HealthComponent = require(game:GetService("ReplicatedStorage").ECS.Components.HealthComponent)
local DamageComponent = require(game:GetService("ReplicatedStorage").ECS.Components.DamageComponent)

local PlayerService = Knit.CreateService({
	Name = "PlayerService",
	Client = {
		-- 客戶端可調用的方法
		GetPlayerData = Knit.CreateSignal(),
		PlayerDataUpdated = Knit.CreateSignal(),
		PlayerJoined = Knit.CreateSignal(),
		PlayerLeft = Knit.CreateSignal(),
	},
})

-- 私有變量
local playerEntities = {} -- 玩家實體映射 {[player] = entityId}
local playerProfiles = {} -- 玩家檔案映射 {[player] = profile}

function PlayerService:KnitStart()
	print("🎯 PlayerService started (ECS Version)")
	
	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found!")
		return
	end
	
	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:_onPlayerAdded(player)
	end)
	
	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		self:_onPlayerRemoving(player)
	end)
	
	-- 處理已經在遊戲中的玩家
	for _, player in pairs(Players:GetPlayers()) do
		self:_onPlayerAdded(player)
	end
end

function PlayerService:KnitInit()
	-- 初始化時不需要獲取其他服務
end

-- 玩家加入處理
function PlayerService:_onPlayerAdded(player)
	print("👋 Player joined:", player.Name)
	
	-- 等待角色生成
	local character = player.Character or player.CharacterAdded:Wait()
	
	-- 載入玩家檔案
	self:_loadPlayerProfile(player):andThen(function(profile)
		playerProfiles[player] = profile
		
		-- 創建玩家實體
		local entityId = self:_createPlayerEntity(player, profile.Data)
		if entityId then
			playerEntities[player] = entityId
			
			-- 通知客戶端玩家加入
			self.Client.PlayerJoined:FireAll(player.Name, player.UserId)
			self.Client.GetPlayerData:Fire(player, profile.Data)
		end
		
	end):catch(function(err)
		warn("❌ Failed to load player profile:", err)
		player:Kick("數據載入失敗，請重新加入遊戲")
	end)
end

-- 玩家離開處理
function PlayerService:_onPlayerRemoving(player)
	print("👋 Player leaving:", player.Name)
	
	-- 保存玩家檔案
	local profile = playerProfiles[player]
	if profile then
		-- 從實體同步數據到檔案
		self:_syncEntityToProfile(player)
		
		-- 釋放檔案
		profile:Release()
		playerProfiles[player] = nil
	end
	
	-- 移除玩家實體
	local entityId = playerEntities[player]
	if entityId and self.world then
		self.world:despawn(entityId)
		playerEntities[player] = nil
	end
	
	-- 通知客戶端玩家離開
	self.Client.PlayerLeft:FireAll(player.Name, player.UserId)
end

-- 載入玩家檔案
function PlayerService:_loadPlayerProfile(player)
	local Promise = require(game:GetService("ReplicatedStorage").Shared.Modules.Utility.Promise)
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	return Promise.new(function(resolve, reject)
		local profile = PlayerProfile.loadProfile(player)
		if profile then
			resolve(profile)
		else
			reject("Failed to load profile")
		end
	end)
end

-- 創建玩家實體
function PlayerService:_createPlayerEntity(player, profileData)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		warn("❌ Player character not ready:", player.Name)
		return nil
	end
	
	local character = player.Character
	local humanoidRootPart = character.HumanoidRootPart
	
	-- 創建玩家實體
	local entityId = self.world:spawn(
		PositionComponent({
			position = humanoidRootPart.Position,
			rotation = humanoidRootPart.CFrame,
			lastPosition = humanoidRootPart.Position,
			velocity = Vector3.new(),
		}),
		HealthComponent({
			current = profileData.stats.currentHealth or 100,
			maximum = profileData.stats.maxHealth or 100,
			regeneration = 1, -- 每秒回血1點
			lastDamageTime = 0,
			isDead = false,
		}),
		DamageComponent({
			attack = profileData.stats.attack or 20,
			defense = profileData.stats.defense or 5,
			criticalChance = profileData.stats.criticalChance or 0.1,
			criticalMultiplier = profileData.stats.criticalMultiplier or 2.0,
			elementalType = "None",
			elementalDamage = 0,
		})
	)
	
	print("🎯 Created player entity:", entityId, "for", player.Name)
	return entityId
end

-- 從實體同步數據到檔案
function PlayerService:_syncEntityToProfile(player)
	local entityId = playerEntities[player]
	local profile = playerProfiles[player]
	
	if not entityId or not profile or not self.world then return end
	
	local health = self.world:get(entityId, HealthComponent)
	local damage = self.world:get(entityId, DamageComponent)
	
	if health then
		profile.Data.stats.currentHealth = health.current
		profile.Data.stats.maxHealth = health.maximum
	end
	
	if damage then
		profile.Data.stats.attack = damage.attack
		profile.Data.stats.defense = damage.defense
		profile.Data.stats.criticalChance = damage.criticalChance
		profile.Data.stats.criticalMultiplier = damage.criticalMultiplier
	end
end

-- 獲取玩家實體ID
function PlayerService:GetPlayerEntityId(player)
	return playerEntities[player]
end

-- 獲取玩家檔案
function PlayerService:GetPlayerProfile(player)
	return playerProfiles[player]
end

-- 更新玩家位置（從角色同步到實體）
function PlayerService:UpdatePlayerPosition(player)
	local entityId = playerEntities[player]
	if not entityId or not self.world then return end

	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		local humanoidRootPart = player.Character.HumanoidRootPart
		PlayerEntity.updatePosition(self.world, entityId, humanoidRootPart.Position, humanoidRootPart.CFrame)
	end
end

-- 客戶端請求玩家數據
function PlayerService.Client:GetPlayerData(player)
	local profile = PlayerService:GetPlayerProfile(player)
	if profile then
		return profile.Data
	end
	return nil
end

return PlayerService
