--[[
	SimplePetTest.lua - 簡單的寵物召喚測試
	快速檢查寵物召喚的基本功能
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🐾 Simple Pet Test Starting...")

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待服務初始化
	task.wait(2)
	
	-- 獲取服務
	local PetService = Knit.GetService("PetService")
	
	if not PetService then
		warn("❌ PetService not found!")
		return
	end
	
	print("✅ PetService found")
	
	-- 檢查 Matter World
	if _G.MatterWorld then
		print("✅ Matter World available")
		local entityCount = 0
		for _ in _G.MatterWorld:query() do
			entityCount = entityCount + 1
		end
		print("  Current entities:", entityCount)
	else
		warn("❌ Matter World not found!")
	end
	
	-- 檢查玩家檔案
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Player profile found")
	print("  Owned pets:", profile.Data.ownedPets)
	
	-- 確保有史萊姆寵物
	if not profile.Data.ownedPets.Slime then
		print("💰 Adding Slime pet...")
		PlayerProfile.addPet(player, "Slime", {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
		print("  Slime pet added")
	end
	
	-- 監聽召喚事件
	local summonReceived = false
	PetService.PetSummoned:Connect(function(petId)
		summonReceived = true
		print("🎉 Pet summoned signal received:", petId)
		
		-- 檢查 workspace 中的寵物模型
		task.spawn(function()
			for i = 1, 10 do
				for _, child in ipairs(workspace:GetChildren()) do
					if child.Name:match("Pet_") then
						print("✅ Pet model found:", child.Name)
						if child.PrimaryPart then
							print("  Position:", child.PrimaryPart.Position)
						end
						return
					end
				end
				task.wait(0.5)
			end
			print("❌ No pet model found in workspace")
		end)
	end)
	
	-- 嘗試召喚寵物
	print("\n🧪 Attempting to summon Slime...")
	PetService.SummonPet:Fire("Slime")
	
	-- 等待結果
	task.wait(5)
	
	if summonReceived then
		print("✅ Pet summoning works!")
	else
		print("❌ Pet summoning failed - no signal received")
		
		-- 檢查可能的問題
		print("\n🔍 Debugging information:")
		print("  Player name:", player.Name)
		print("  Player in game:", player.Parent == game.Players)
		print("  Player character:", player.Character and "exists" or "missing")
		
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			print("  Player position:", player.Character.HumanoidRootPart.Position)
		end
	end
	
	print("\n🧪 Test completed!")
	
end):catch(function(err)
	warn("❌ Test failed with error:", err)
end)

return true
