--[[
	EffectPool - 特效對象池系統
	重用特效對象以減少創建和銷毀的開銷
]]

local EffectPool = {}

-- 私有變量
local pools = {} -- 各種特效的對象池
local activeEffects = {} -- 當前活躍的特效
local TweenService = game:GetService("TweenService")

-- 特效類型定義
local EFFECT_TYPES = {
	EXPLOSION = "Explosion",
	SWORD_TRAIL = "SwordTrail", 
	SLASH_MARK = "SlashMark",
	DAMAGE_NUMBER = "DamageNumber",
	SWORD_BEAM = "SwordBeam"
}

-- 初始化對象池
local function initializePool(effectType, initialSize)
	if not pools[effectType] then
		pools[effectType] = {
			available = {},
			inUse = {},
			createFunction = nil,
			resetFunction = nil
		}
	end
	
	-- 預創建對象
	for i = 1, initialSize do
		local effect = EffectPool.createEffect(effectType)
		if effect then
			effect.Parent = nil -- 暫時移除父級
			table.insert(pools[effectType].available, effect)
		end
	end
	
	print("✨ Effect pool initialized:", effectType, "with", initialSize, "objects")
end

-- 創建爆炸特效
local function createExplosion()
	local explosion = Instance.new("Explosion")
	explosion.BlastRadius = 8
	explosion.BlastPressure = 0
	return explosion
end

-- 創建劍光軌跡
local function createSwordTrail()
	local trail = Instance.new("Part")
	trail.Name = "SwordTrail"
	trail.Size = Vector3.new(0.2, 0.2, 6)
	trail.Material = Enum.Material.Neon
	trail.BrickColor = BrickColor.new("Cyan")
	trail.CanCollide = false
	trail.Anchored = true
	trail.Transparency = 0.3
	return trail
end

-- 創建斬擊痕跡
local function createSlashMark()
	local slashMark = Instance.new("Part")
	slashMark.Name = "SlashMark"
	slashMark.Size = Vector3.new(0.1, 4, 0.1)
	slashMark.Material = Enum.Material.Neon
	slashMark.BrickColor = BrickColor.new("Bright yellow")
	slashMark.CanCollide = false
	slashMark.Anchored = true
	return slashMark
end

-- 創建傷害數字
local function createDamageNumber()
	local gui = Instance.new("BillboardGui")
	gui.Name = "DamageNumber"
	gui.Size = UDim2.new(0, 100, 0, 50)
	gui.StudsOffset = Vector3.new(0, 2, 0)
	
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(1, 0, 1, 0)
	label.BackgroundTransparency = 1
	label.TextScaled = true
	label.Font = Enum.Font.GothamBold
	label.TextStrokeTransparency = 0
	label.TextStrokeColor3 = Color3.new(0, 0, 0)
	label.Parent = gui
	
	return gui
end

-- 創建劍光束
local function createSwordBeam()
	local beam = Instance.new("Part")
	beam.Name = "SwordBeam"
	beam.Size = Vector3.new(0.5, 0.5, 10)
	beam.Material = Enum.Material.Neon
	beam.BrickColor = BrickColor.new("Cyan")
	beam.CanCollide = false
	beam.Anchored = true
	beam.Transparency = 0.3
	return beam
end

-- 特效創建函數映射
local createFunctions = {
	[EFFECT_TYPES.EXPLOSION] = createExplosion,
	[EFFECT_TYPES.SWORD_TRAIL] = createSwordTrail,
	[EFFECT_TYPES.SLASH_MARK] = createSlashMark,
	[EFFECT_TYPES.DAMAGE_NUMBER] = createDamageNumber,
	[EFFECT_TYPES.SWORD_BEAM] = createSwordBeam
}

-- 創建特效對象
function EffectPool.createEffect(effectType)
	local createFunc = createFunctions[effectType]
	if createFunc then
		return createFunc()
	end
	warn("EffectPool: Unknown effect type:", effectType)
	return nil
end

-- 重置特效對象
local function resetEffect(effect, effectType)
	if effectType == EFFECT_TYPES.EXPLOSION then
		-- 爆炸特效無需重置
	elseif effectType == EFFECT_TYPES.SWORD_TRAIL then
		effect.Transparency = 0.3
		effect.Size = Vector3.new(0.2, 0.2, 6)
	elseif effectType == EFFECT_TYPES.SLASH_MARK then
		effect.Transparency = 0
		effect.Size = Vector3.new(0.1, 4, 0.1)
	elseif effectType == EFFECT_TYPES.DAMAGE_NUMBER then
		local label = effect:FindFirstChild("TextLabel")
		if label then
			label.TextTransparency = 0
			label.TextColor3 = Color3.new(1, 1, 1)
		end
		effect.StudsOffset = Vector3.new(0, 2, 0)
	elseif effectType == EFFECT_TYPES.SWORD_BEAM then
		effect.Transparency = 0.3
		effect.Size = Vector3.new(0.5, 0.5, 10)
	end
end

-- 獲取特效對象
function EffectPool.getEffect(effectType)
	if not pools[effectType] then
		initializePool(effectType, 5) -- 默認創建5個對象
	end
	
	local pool = pools[effectType]
	local effect = nil
	
	-- 從可用池中取出對象
	if #pool.available > 0 then
		effect = table.remove(pool.available)
	else
		-- 如果池中沒有可用對象，創建新的
		effect = EffectPool.createEffect(effectType)
		print("✨ Created new effect object:", effectType)
	end
	
	if effect then
		-- 重置對象狀態
		resetEffect(effect, effectType)
		
		-- 移動到使用中池
		table.insert(pool.inUse, effect)
		activeEffects[effect] = effectType
	end
	
	return effect
end

-- 歸還特效對象
function EffectPool.returnEffect(effect)
	local effectType = activeEffects[effect]
	if not effectType then
		warn("EffectPool: Trying to return unknown effect")
		return
	end
	
	local pool = pools[effectType]
	if not pool then return end
	
	-- 從使用中池移除
	for i, usedEffect in ipairs(pool.inUse) do
		if usedEffect == effect then
			table.remove(pool.inUse, i)
			break
		end
	end
	
	-- 清理對象
	effect.Parent = nil
	activeEffects[effect] = nil
	
	-- 歸還到可用池
	table.insert(pool.available, effect)
end

-- 自動歸還特效（帶延遲）
function EffectPool.autoReturnEffect(effect, delay)
	delay = delay or 2 -- 默認2秒後歸還
	
	task.wait(delay)
	EffectPool.returnEffect(effect)
end

-- 創建並使用特效的便捷函數
function EffectPool.playEffect(effectType, position, properties, duration)
	local effect = EffectPool.getEffect(effectType)
	if not effect then return nil end
	
	-- 設置位置
	if effectType == EFFECT_TYPES.EXPLOSION then
		effect.Position = position
		effect.Parent = workspace
	else
		effect.CFrame = CFrame.new(position)
		effect.Parent = workspace
	end
	
	-- 應用自定義屬性
	if properties then
		for property, value in pairs(properties) do
			if effect[property] then
				effect[property] = value
			end
		end
	end
	
	-- 自動歸還
	task.spawn(function()
		EffectPool.autoReturnEffect(effect, duration or 2)
	end)
	
	return effect
end

-- 獲取池統計信息
function EffectPool.getStats()
	local stats = {}
	for effectType, pool in pairs(pools) do
		stats[effectType] = {
			available = #pool.available,
			inUse = #pool.inUse,
			total = #pool.available + #pool.inUse
		}
	end
	return stats
end

-- 清理所有池
function EffectPool.clearAll()
	for effectType, pool in pairs(pools) do
		-- 銷毀所有對象
		for _, effect in ipairs(pool.available) do
			if effect and effect.Parent then
				effect:Destroy()
			end
		end
		for _, effect in ipairs(pool.inUse) do
			if effect and effect.Parent then
				effect:Destroy()
			end
		end
	end
	
	pools = {}
	activeEffects = {}
	print("✨ All effect pools cleared")
end

-- 預熱池（預創建常用特效）
function EffectPool.warmUp()
	initializePool(EFFECT_TYPES.EXPLOSION, 3)
	initializePool(EFFECT_TYPES.SWORD_TRAIL, 5)
	initializePool(EFFECT_TYPES.SLASH_MARK, 5)
	initializePool(EFFECT_TYPES.DAMAGE_NUMBER, 10)
	initializePool(EFFECT_TYPES.SWORD_BEAM, 3)
	print("✨ Effect pools warmed up")
end

-- 導出特效類型常量
EffectPool.TYPES = EFFECT_TYPES

return EffectPool
