--[[
	Promise.lua - Promise 實現
	提供異步操作的 Promise 模式
]]

local Promise = {}
Promise.__index = Promise

-- Promise 狀態
local PENDING = "Pending"
local RESOLVED = "Resolved"
local REJECTED = "Rejected"

function Promise.new(executor)
	if type(executor) ~= "function" then
		error("Promise executor must be a function", 2)
	end
	
	local self = setmetatable({
		_state = PENDING,
		_value = nil,
		_handlers = {},
	}, Promise)
	
	local function resolve(value)
		if self._state == PENDING then
			self._state = RESOLVED
			self._value = value
			self:_executeHandlers()
		end
	end
	
	local function reject(reason)
		if self._state == PENDING then
			self._state = REJECTED
			self._value = reason
			self:_executeHandlers()
		end
	end
	
	-- 執行 executor
	local success, result = pcall(executor, resolve, reject)
	if not success then
		reject(result)
	end
	
	return self
end

-- 創建已解決的 Promise
function Promise.resolve(value)
	return Promise.new(function(resolve)
		resolve(value)
	end)
end

-- 創建已拒絕的 Promise
function Promise.reject(reason)
	return Promise.new(function(_, reject)
		reject(reason)
	end)
end

-- 等待所有 Promise 完成
function Promise.all(promises)
	if type(promises) ~= "table" then
		return Promise.reject("Promise.all expects a table")
	end
	
	return Promise.new(function(resolve, reject)
		local results = {}
		local completed = 0
		local total = #promises
		
		if total == 0 then
			resolve({})
			return
		end
		
		for i, promise in ipairs(promises) do
			if not promise or type(promise.andThen) ~= "function" then
				reject("All items must be Promises")
				return
			end
			
			promise:andThen(function(value)
				results[i] = value
				completed = completed + 1
				
				if completed == total then
					resolve(results)
				end
			end, function(reason)
				reject(reason)
			end)
		end
	end)
end

-- 等待任一 Promise 完成
function Promise.race(promises)
	if type(promises) ~= "table" then
		return Promise.reject("Promise.race expects a table")
	end
	
	return Promise.new(function(resolve, reject)
		for _, promise in ipairs(promises) do
			if not promise or type(promise.andThen) ~= "function" then
				reject("All items must be Promises")
				return
			end
			
			promise:andThen(resolve, reject)
		end
	end)
end

-- 延遲執行
function Promise.delay(seconds)
	return Promise.new(function(resolve)
		task.wait(seconds)
		resolve()
	end)
end

-- andThen 方法
function Promise:andThen(onResolved, onRejected)
	return Promise.new(function(resolve, reject)
		local function handle()
			if self._state == RESOLVED then
				if type(onResolved) == "function" then
					local success, result = pcall(onResolved, self._value)
					if success then
						if result and type(result.andThen) == "function" then
							-- 返回的是 Promise
							result:andThen(resolve, reject)
						else
							resolve(result)
						end
					else
						reject(result)
					end
				else
					resolve(self._value)
				end
			elseif self._state == REJECTED then
				if type(onRejected) == "function" then
					local success, result = pcall(onRejected, self._value)
					if success then
						if result and type(result.andThen) == "function" then
							-- 返回的是 Promise
							result:andThen(resolve, reject)
						else
							resolve(result)
						end
					else
						reject(result)
					end
				else
					reject(self._value)
				end
			end
		end
		
		if self._state == PENDING then
			table.insert(self._handlers, handle)
		else
			task.spawn(handle)
		end
	end)
end

-- catch 方法
function Promise:catch(onRejected)
	return self:andThen(nil, onRejected)
end

-- finally 方法
function Promise:finally(onFinally)
	return self:andThen(
		function(value)
			if type(onFinally) == "function" then
				onFinally()
			end
			return value
		end,
		function(reason)
			if type(onFinally) == "function" then
				onFinally()
			end
			error(reason)
		end
	)
end

-- 等待 Promise 完成
function Promise:await()
	if self._state ~= PENDING then
		if self._state == RESOLVED then
			return self._value
		else
			error(self._value)
		end
	end
	
	local bindableEvent = Instance.new("BindableEvent")
	local result
	local success
	
	self:andThen(function(value)
		result = value
		success = true
		bindableEvent:Fire()
	end, function(reason)
		result = reason
		success = false
		bindableEvent:Fire()
	end)
	
	bindableEvent.Event:Wait()
	bindableEvent:Destroy()
	
	if success then
		return result
	else
		error(result)
	end
end

-- 獲取 Promise 狀態
function Promise:getState()
	return self._state
end

-- 檢查是否已完成
function Promise:isResolved()
	return self._state == RESOLVED
end

function Promise:isRejected()
	return self._state == REJECTED
end

function Promise:isPending()
	return self._state == PENDING
end

-- 執行處理器
function Promise:_executeHandlers()
	for _, handler in ipairs(self._handlers) do
		task.spawn(handler)
	end
	self._handlers = {}
end

return Promise
