--[[
	PlayerService - 玩家管理服務
	負責處理玩家加入、離開和基本數據管理
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

local PlayerService = Knit.CreateService({
	Name = "PlayerService",
	Client = {
		-- 客戶端可調用的方法
		GetPlayerData = Knit.CreateSignal(),
	},
})

-- 私有變量
local playerData = {}

function PlayerService:KnitStart()
	print("🎯 PlayerService started")

	-- 獲取其他服務
	self.PetService = Knit.GetService("PetService")

	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:_onPlayerAdded(player)
	end)

	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		self:_onPlayerRemoving(player)
	end)

	-- 處理已經在遊戲中的玩家
	for _, player in pairs(Players:GetPlayers()) do
		self:_onPlayerAdded(player)
	end
end

function PlayerService:KnitInit()
	-- 初始化服務依賴
	self.PetService = nil -- 將在 KnitStart 後獲取
end

-- 玩家加入處理
function PlayerService:_onPlayerAdded(player)
	print("👋 Player joined:", player.Name)
	
	-- 初始化玩家數據
	playerData[player] = {
		joinTime = tick(),
		-- 更多數據將在 DataService 中處理
	}
	
	-- 通知客戶端
	self.Client.GetPlayerData:Fire(player, playerData[player])
end

-- 玩家離開處理
function PlayerService:_onPlayerRemoving(player)
	print("👋 Player leaving:", player.Name)

	-- 清理寵物
	if self.PetService then
		self.PetService:_cleanupPlayerPets(player)
	end

	-- 清理玩家數據
	if playerData[player] then
		playerData[player] = nil
	end
end

-- 獲取玩家數據
function PlayerService:GetPlayerData(player)
	return playerData[player]
end

return PlayerService
