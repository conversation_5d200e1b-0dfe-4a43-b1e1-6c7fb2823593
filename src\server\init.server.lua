--[[
	服務端初始化腳本
	整合 Knit 框架和 Matter ECS 系統
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)

-- 創建 Matter World
local world = Matter.World.new()

-- 載入所有服務
local Services = script.Services
for _, service in pairs(Services:GetChildren()) do
	if service:IsA("ModuleScript") then
		require(service)
	end
end

-- 載入 ECS 系統
local ECSLoader = require(game:GetService("ReplicatedStorage").Shared.ECS.ECSLoader)
ECSLoader.loadSystems(world)

-- 啟動 Knit
Knit.Start():andThen(function()
	print("🚀 Server started successfully!")
	print("⚙️ Matter World initialized")
	
	-- 啟動 ECS 系統
	ECSLoader.startSystems(world)
	
	-- 開始 ECS 主循環
	game:GetService("RunService").Heartbeat:Connect(function(deltaTime)
		world:step(deltaTime)
	end)
	
end):catch(function(err)
	warn("❌ Server startup failed:", err)
end)

-- 導出 world 供其他模組使用
_G.MatterWorld = world
