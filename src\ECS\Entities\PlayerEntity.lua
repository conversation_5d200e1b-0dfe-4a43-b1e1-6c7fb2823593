--[[
	PlayerEntity.lua - 玩家實體工廠
	創建和管理玩家實體的標準化方法
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local DamageComponent = require(script.Parent.Parent.Components.DamageComponent)
local SwordSwingComponent = require(script.Parent.Parent.Components.SwordSwingComponent)
local TargetComponent = require(script.Parent.Parent.Components.TargetComponent)

local PlayerEntity = {}

-- 玩家組件
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local PlayerComponent = Matter.component("Player", {
	userId = 0,
	displayName = "",
	level = 1,
	experience = 0,
	experienceToNext = 100,
	coins = 0,
	gems = 0,
	joinTime = 0,
	lastActiveTime = 0,
})

-- 移動組件
local MovementComponent = Matter.component("Movement", {
	velocity = Vector3.new(),
	speed = 16,
	acceleration = 50,
	friction = 0.9,
	isMoving = false,
	direction = Vector3.new(),
})

-- 創建玩家實體
function PlayerEntity.create(world, player, playerData)
	if not world or not player then
		warn("❌ Invalid parameters for PlayerEntity.create")
		return nil
	end
	
	-- 獲取玩家角色位置
	local character = player.Character
	local position = Vector3.new(0, 10, 0)
	local rotation = CFrame.new()
	
	if character and character:FindFirstChild("HumanoidRootPart") then
		local humanoidRootPart = character.HumanoidRootPart
		position = humanoidRootPart.Position
		rotation = humanoidRootPart.CFrame
	end
	
	-- 計算玩家屬性
	local stats = playerData and playerData.stats or {}
	local level = playerData and playerData.level or 1
	
	-- 創建玩家實體
	local entityId = world:spawn(
		PlayerComponent({
			userId = player.UserId,
			displayName = player.DisplayName,
			level = level,
			experience = playerData and playerData.experience or 0,
			experienceToNext = level * 100,
			coins = playerData and playerData.coins or 100,
			gems = playerData and playerData.gems or 0,
			joinTime = tick(),
			lastActiveTime = tick(),
		}),
		
		PositionComponent({
			position = position,
			rotation = rotation,
			lastPosition = position,
			velocity = Vector3.new(),
		}),
		
		HealthComponent({
			current = stats.currentHealth or 100,
			maximum = stats.maxHealth or 100,
			regeneration = 1.0, -- 每秒回血1點
			lastDamageTime = 0,
			isDead = false,
		}),
		
		DamageComponent({
			attack = stats.attack or 20,
			defense = stats.defense or 5,
			criticalChance = stats.criticalChance or 0.1,
			criticalMultiplier = stats.criticalMultiplier or 2.0,
			elementalType = "None",
			elementalDamage = 0,
		}),
		
		MovementComponent({
			velocity = Vector3.new(),
			speed = 16,
			acceleration = 50,
			friction = 0.9,
			isMoving = false,
			direction = Vector3.new(),
		}),
		
		SwordSwingComponent({
			isSwinging = false,
			swingDuration = 0.8,
			swingStartTime = 0,
			range = 8,
			damage = stats.attack or 20,
			weaponId = "",
			hasHit = {},
		}),
		
		TargetComponent({
			targetId = 0,
			targetType = "Monster",
			lastTargetTime = 0,
			attackRange = 8,
			canAttack = true,
			lastAttackTime = 0,
			attackCooldown = 1.5,
		})
	)
	
	print("🎯 Created PlayerEntity:", entityId, "for", player.Name)
	return entityId
end

-- 更新玩家實體數據
function PlayerEntity.updateData(world, entityId, playerData)
	if not world or not entityId or not playerData then return false end
	
	-- 更新玩家組件
	local playerComponent = world:get(entityId, PlayerComponent)
	if playerComponent then
		world:insert(entityId, playerComponent:patch({
			level = playerData.level or playerComponent.level,
			experience = playerData.experience or playerComponent.experience,
			experienceToNext = (playerData.level or playerComponent.level) * 100,
			coins = playerData.coins or playerComponent.coins,
			gems = playerData.gems or playerComponent.gems,
			lastActiveTime = tick(),
		}))
	end
	
	-- 更新血量組件
	local healthComponent = world:get(entityId, HealthComponent)
	if healthComponent and playerData.stats then
		world:insert(entityId, healthComponent:patch({
			maximum = playerData.stats.maxHealth or healthComponent.maximum,
			current = math.min(playerData.stats.currentHealth or healthComponent.current, playerData.stats.maxHealth or healthComponent.maximum),
		}))
	end
	
	-- 更新傷害組件
	local damageComponent = world:get(entityId, DamageComponent)
	if damageComponent and playerData.stats then
		world:insert(entityId, damageComponent:patch({
			attack = playerData.stats.attack or damageComponent.attack,
			defense = playerData.stats.defense or damageComponent.defense,
			criticalChance = playerData.stats.criticalChance or damageComponent.criticalChance,
			criticalMultiplier = playerData.stats.criticalMultiplier or damageComponent.criticalMultiplier,
		}))
	end
	
	return true
end

-- 更新玩家位置
function PlayerEntity.updatePosition(world, entityId, newPosition, newRotation)
	if not world or not entityId then return false end
	
	local positionComponent = world:get(entityId, PositionComponent)
	if positionComponent then
		world:insert(entityId, positionComponent:patch({
			lastPosition = positionComponent.position,
			position = newPosition or positionComponent.position,
			rotation = newRotation or positionComponent.rotation,
		}))
		return true
	end
	
	return false
end

-- 設置玩家移動
function PlayerEntity.setMovement(world, entityId, velocity, direction, isMoving)
	if not world or not entityId then return false end
	
	local movementComponent = world:get(entityId, MovementComponent)
	if movementComponent then
		world:insert(entityId, movementComponent:patch({
			velocity = velocity or movementComponent.velocity,
			direction = direction or movementComponent.direction,
			isMoving = isMoving ~= nil and isMoving or movementComponent.isMoving,
		}))
		return true
	end
	
	return false
end

-- 治療玩家
function PlayerEntity.heal(world, entityId, healAmount)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	if healthComponent and not healthComponent.isDead then
		local newHealth = math.min(healthComponent.maximum, healthComponent.current + healAmount)
		world:insert(entityId, healthComponent:patch({
			current = newHealth,
		}))
		return newHealth
	end
	
	return false
end

-- 增加經驗值
function PlayerEntity.addExperience(world, entityId, expAmount)
	if not world or not entityId then return false end
	
	local playerComponent = world:get(entityId, PlayerComponent)
	if playerComponent then
		local newExp = playerComponent.experience + expAmount
		local newLevel = playerComponent.level
		local expToNext = playerComponent.experienceToNext
		
		-- 檢查升級
		while newExp >= expToNext do
			newExp = newExp - expToNext
			newLevel = newLevel + 1
			expToNext = newLevel * 100
		end
		
		world:insert(entityId, playerComponent:patch({
			experience = newExp,
			level = newLevel,
			experienceToNext = expToNext,
		}))
		
		-- 如果升級了，增加屬性
		if newLevel > playerComponent.level then
			PlayerEntity.levelUpStats(world, entityId, newLevel - playerComponent.level)
			return newLevel -- 返回新等級表示升級了
		end
		
		return true
	end
	
	return false
end

-- 升級時增加屬性
function PlayerEntity.levelUpStats(world, entityId, levelIncrease)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	local damageComponent = world:get(entityId, DamageComponent)
	
	-- 每級增加的屬性
	local healthPerLevel = 10
	local attackPerLevel = 2
	local defensePerLevel = 1
	
	if healthComponent then
		local healthIncrease = healthPerLevel * levelIncrease
		world:insert(entityId, healthComponent:patch({
			maximum = healthComponent.maximum + healthIncrease,
			current = healthComponent.current + healthIncrease, -- 升級時回滿血
		}))
	end
	
	if damageComponent then
		world:insert(entityId, damageComponent:patch({
			attack = damageComponent.attack + (attackPerLevel * levelIncrease),
			defense = damageComponent.defense + (defensePerLevel * levelIncrease),
		}))
	end
	
	return true
end

-- 獲取玩家數據
function PlayerEntity.getData(world, entityId)
	if not world or not entityId then return nil end
	
	local playerComponent = world:get(entityId, PlayerComponent)
	local healthComponent = world:get(entityId, HealthComponent)
	local damageComponent = world:get(entityId, DamageComponent)
	local positionComponent = world:get(entityId, PositionComponent)
	
	return {
		player = playerComponent,
		health = healthComponent,
		damage = damageComponent,
		position = positionComponent,
	}
end

-- 檢查玩家是否存活
function PlayerEntity.isAlive(world, entityId)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	return healthComponent and not healthComponent.isDead and healthComponent.current > 0
end

-- 復活玩家
function PlayerEntity.revive(world, entityId, healthPercent)
	if not world or not entityId then return false end
	
	local healthComponent = world:get(entityId, HealthComponent)
	if healthComponent and healthComponent.isDead then
		local newHealth = math.floor(healthComponent.maximum * (healthPercent or 0.5))
		world:insert(entityId, healthComponent:patch({
			current = newHealth,
			isDead = false,
		}))
		return true
	end
	
	return false
end

-- 導出組件供其他模組使用
PlayerEntity.PlayerComponent = PlayerComponent
PlayerEntity.MovementComponent = MovementComponent

return PlayerEntity
