--[[
	PlayerProfile.lua - 玩家檔案管理
	使用 ProfileService 處理玩家數據持久化
]]

local ProfileService = require(game:GetService("ServerStorage").ServerPackages.profileservice)

local PlayerProfile = {}

-- 預設玩家數據模板
local DEFAULT_PROFILE_TEMPLATE = {
	-- 基本資料
	level = 1,
	experience = 0,
	coins = 100,
	gems = 0,
	
	-- 玩家屬性
	stats = {
		maxHealth = 100,
		currentHealth = 100,
		attack = 20,
		defense = 5,
		criticalChance = 0.1,
		criticalMultiplier = 2.0,
	},
	
	-- 寵物相關
	ownedPets = {
		-- 預設給一隻史萊姆
		Slime = {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = 0,
		}
	},
	activePet = nil,
	petDex = {
		-- 寵物圖鑑解鎖狀態
		Slime = true,
	},
	equippedPets = {}, -- 最多3隻寵物同時裝備
	
	-- 武器相關
	ownedWeapons = {
		-- 預設給一把木劍
		WoodenSword = {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = 0,
		}
	},
	equippedWeapon = "WoodenSword",
	weaponDex = {
		WoodenSword = true,
	},
	
	-- 抽卡相關
	gachaHistory = {},
	lastGachaTime = 0,
	gachaPity = {
		rare = 0,
		epic = 0,
		legendary = 0,
	},
	
	-- 成就與任務
	achievements = {},
	dailyQuests = {},
	weeklyQuests = {},
	
	-- 設定
	settings = {
		language = "zh-TW",
		musicVolume = 0.5,
		sfxVolume = 0.7,
		autoAttack = true,
		showDamageNumbers = true,
	},
	
	-- 統計數據
	statistics = {
		totalPlayTime = 0,
		monstersKilled = 0,
		damageDealt = 0,
		damageTaken = 0,
		gachaRolls = 0,
		coinsEarned = 0,
		coinsSpent = 0,
		petsObtained = 1, -- 預設有史萊姆
		weaponsObtained = 1, -- 預設有木劍
		loginDays = 0,
		lastLoginTime = 0,
	},
	
	-- 版本控制
	dataVersion = 1,
	createdTime = 0,
	lastSaveTime = 0,
}

-- ProfileStore 設定
local profileStore = ProfileService.GetProfileStore(
	"PlayerData_v1",
	DEFAULT_PROFILE_TEMPLATE
)

-- 活躍的檔案
local profiles = {}

-- 載入玩家檔案
function PlayerProfile.loadProfile(player)
	local profile = profileStore:LoadProfileAsync("Player_" .. player.UserId)
	
	if profile ~= nil then
		profile:AddUserId(player.UserId)
		profile:Reconcile()
		
		profile:ListenToRelease(function()
			profiles[player] = nil
			player:Kick("數據釋放 - 請重新加入遊戲")
		end)
		
		if player.Parent == game:GetService("Players") then
			profiles[player] = profile
			
			-- 更新登入時間
			local currentTime = os.time()
			profile.Data.statistics.lastLoginTime = currentTime
			if profile.Data.createdTime == 0 then
				profile.Data.createdTime = currentTime
			end
			
			print("✅ Profile loaded for:", player.Name)
			return profile
		else
			profile:Release()
		end
	else
		print("❌ Failed to load profile for:", player.Name)
		return nil
	end
end

-- 釋放玩家檔案
function PlayerProfile.releaseProfile(player)
	local profile = profiles[player]
	if profile ~= nil then
		-- 更新最後保存時間
		profile.Data.lastSaveTime = os.time()
		
		profile:Release()
		profiles[player] = nil
		print("💾 Profile released for:", player.Name)
	end
end

-- 獲取玩家檔案
function PlayerProfile.getProfile(player)
	return profiles[player]
end

-- 更新玩家數據
function PlayerProfile.updateData(player, key, value)
	local profile = profiles[player]
	if profile then
		profile.Data[key] = value
		return true
	end
	return false
end

-- 增加玩家金幣
function PlayerProfile.addCoins(player, amount)
	local profile = profiles[player]
	if profile then
		profile.Data.coins = profile.Data.coins + amount
		profile.Data.statistics.coinsEarned = profile.Data.statistics.coinsEarned + amount
		return profile.Data.coins
	end
	return 0
end

-- 扣除玩家金幣
function PlayerProfile.spendCoins(player, amount)
	local profile = profiles[player]
	if profile and profile.Data.coins >= amount then
		profile.Data.coins = profile.Data.coins - amount
		profile.Data.statistics.coinsSpent = profile.Data.statistics.coinsSpent + amount
		return true
	end
	return false
end

-- 添加寵物
function PlayerProfile.addPet(player, petId, petData)
	local profile = profiles[player]
	if profile then
		profile.Data.ownedPets[petId] = petData or {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		}
		profile.Data.petDex[petId] = true
		profile.Data.statistics.petsObtained = profile.Data.statistics.petsObtained + 1
		return true
	end
	return false
end

-- 添加武器
function PlayerProfile.addWeapon(player, weaponId, weaponData)
	local profile = profiles[player]
	if profile then
		profile.Data.ownedWeapons[weaponId] = weaponData or {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		}
		profile.Data.weaponDex[weaponId] = true
		profile.Data.statistics.weaponsObtained = profile.Data.statistics.weaponsObtained + 1
		return true
	end
	return false
end

-- 增加經驗值
function PlayerProfile.addExperience(player, amount)
	local profile = profiles[player]
	if profile then
		profile.Data.experience = profile.Data.experience + amount
		
		-- 檢查升級
		local expNeeded = profile.Data.level * 100
		while profile.Data.experience >= expNeeded do
			profile.Data.experience = profile.Data.experience - expNeeded
			profile.Data.level = profile.Data.level + 1
			expNeeded = profile.Data.level * 100
			
			-- 升級時增加屬性
			profile.Data.stats.maxHealth = profile.Data.stats.maxHealth + 10
			profile.Data.stats.attack = profile.Data.stats.attack + 2
			profile.Data.stats.defense = profile.Data.stats.defense + 1
			
			print("🎉 Player", player.Name, "leveled up to", profile.Data.level)
		end
		
		return profile.Data.level
	end
	return 1
end

-- 保存所有檔案
function PlayerProfile.saveAllProfiles()
	for player, profile in pairs(profiles) do
		if profile then
			profile.Data.lastSaveTime = os.time()
		end
	end
	print("💾 All profiles saved")
end

-- 定期保存（每5分鐘）
game:GetService("RunService").Heartbeat:Connect(function()
	-- 這裡可以添加定期保存邏輯
end)

-- 遊戲關閉時保存所有檔案
game:BindToClose(function()
	PlayerProfile.saveAllProfiles()
	wait(2) -- 等待保存完成
end)

return PlayerProfile
