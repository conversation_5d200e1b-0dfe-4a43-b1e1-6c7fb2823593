--[[
	寵物跟隨系統
	處理寵物跟隨主人的邏輯
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(script.Parent.Parent.Components)
local RunService = game:GetService("RunService")

local function PetFollowSystem(world, deltaTime)
	-- 查詢所有需要跟隨的寵物
	for petId, pet, followTarget, position, movement in world:query(Components.Pet, Components.FollowTarget, Components.Position, Components.Movement) do
		if followTarget.isFollowing then
			-- 查找主人的位置
			local ownerPosition = nil
			
			for ownerId, ownerPos, player in world:query(Components.Position, Components.Player) do
				if player.userId == pet.ownerId then
					ownerPosition = ownerPos.position
					break
				end
			end
			
			if ownerPosition then
				local petPosition = position.position
				local distance = (ownerPosition - petPosition).Magnitude
				
				-- 如果距離超過跟隨距離，開始移動
				if distance > followTarget.followDistance then
					-- 計算移動方向
					local direction = (ownerPosition - petPosition).Unit
					local targetPosition = ownerPosition - direction * followTarget.followDistance
					
					-- 計算移動速度
					local moveSpeed = followTarget.speed
					
					-- 如果距離很遠，增加移動速度
					if distance > followTarget.followDistance * 3 then
						moveSpeed = moveSpeed * 2
					end
					
					-- 計算新的速度向量
					local moveDirection = (targetPosition - petPosition).Unit
					local newVelocity = moveDirection * moveSpeed
					
					-- 更新移動組件
					world:insert(petId, movement:patch({
						velocity = newVelocity,
						speed = moveSpeed,
					}))
					
					-- 更新位置（簡單的移動邏輯）
					local newPosition = petPosition + newVelocity * deltaTime
					
					-- 確保寵物不會穿過地面
					newPosition = Vector3.new(newPosition.X, math.max(newPosition.Y, ownerPosition.Y), newPosition.Z)
					
					world:insert(petId, position:patch({
						position = newPosition,
						rotation = CFrame.lookAt(newPosition, newPosition + moveDirection),
					}))
					
					-- 在 Roblox 中更新實際模型位置
					local petModel = workspace:FindFirstChild("Pet_" .. petId)
					if petModel and petModel.PrimaryPart then
						petModel:SetPrimaryPartCFrame(CFrame.lookAt(newPosition, newPosition + moveDirection))
					end
				else
					-- 距離足夠近，停止移動
					world:insert(petId, movement:patch({
						velocity = Vector3.new(),
						speed = 0,
					}))
				end
			end
		end
	end
end

return PetFollowSystem
