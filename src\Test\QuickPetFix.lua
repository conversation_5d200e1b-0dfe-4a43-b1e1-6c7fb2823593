--[[
	QuickPetFix.lua - 快速修復寵物召喚問題
	直接給玩家添加寵物並測試召喚
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🔧 Quick Pet Fix Starting...")

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待服務初始化
	task.wait(3)
	
	-- 獲取服務
	local PetService = Knit.GetService("PetService")
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	if not PetService then
		warn("❌ PetService not found!")
		return
	end
	
	-- 檢查玩家檔案
	local profile = PlayerProfile.getProfile(player)
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Services and profile found")
	print("  Current pets:", profile.Data.ownedPets)
	
	-- 添加一些測試寵物
	local testPets = {"Slime", "Wolf", "FireSpirit"}
	
	for _, petId in ipairs(testPets) do
		if not profile.Data.ownedPets[petId] then
			print("💰 Adding", petId, "pet...")
			PlayerProfile.addPet(player, petId, {
				level = 1,
				experience = 0,
				rarity = "Common",
				obtainedTime = os.time(),
			})
		end
	end
	
	print("✅ Test pets added")
	print("  Updated pets:", profile.Data.ownedPets)
	
	-- 監聽召喚事件
	PetService.PetSummoned:Connect(function(petId)
		print("🎉 Pet summoned successfully:", petId)
		
		-- 檢查 workspace 中的寵物
		task.spawn(function()
			for i = 1, 10 do
				for _, child in ipairs(workspace:GetChildren()) do
					if child.Name:match("Pet_") then
						print("✅ Pet model found:", child.Name)
						if child.PrimaryPart then
							print("  Position:", child.PrimaryPart.Position)
						end
						return
					end
				end
				task.wait(0.5)
			end
			print("❌ No pet model found in workspace")
		end)
	end)
	
	-- 測試召喚
	print("\n🧪 Testing pet summon...")
	print("Attempting to summon Slime...")
	PetService.SummonPet:Fire("Slime")
	
	task.wait(3)
	
	print("\n🧪 Testing different pets...")
	for _, petId in ipairs(testPets) do
		print("Summoning", petId, "...")
		PetService.SummonPet:Fire(petId)
		task.wait(2)
	end
	
	print("\n✅ Quick fix completed!")
	print("💡 Now try using the UI buttons")
	print("💡 Use /summon command for quick testing")
	
end):catch(function(err)
	warn("❌ Quick fix failed:", err)
end)

return true
