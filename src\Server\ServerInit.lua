--[[
	ServerInit.lua - 服務端初始化腳本
	Knit 服務器初始化，整合 Matter ECS 系統
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local RunService = game:GetService("RunService")

-- 創建 Matter World
local world = Matter.World.new()

-- 載入所有服務
local Services = script.Parent.Services
for _, service in pairs(Services:GetChildren()) do
	if service:IsA("ModuleScript") then
		require(service)
	end
end

-- 載入 ECS 系統
local function loadECSSystems()
	local systems = {}
	local SystemsFolder = game:GetService("ReplicatedStorage").ECS.Systems
	
	for _, systemModule in pairs(SystemsFolder:GetChildren()) do
		if systemModule:IsA("ModuleScript") then
			local systemFunction = require(systemModule)
			if typeof(systemFunction) == "function" then
				table.insert(systems, systemFunction)
				print("📦 Loaded ECS System:", systemModule.Name)
			end
		end
	end
	
	return systems
end

-- 啟動 Knit
Knit.Start():andThen(function()
	print("🚀 Server started successfully!")
	print("⚙️ Matter World initialized")
	
	-- 載入並啟動 ECS 系統
	local systems = loadECSSystems()
	
	-- 開始 ECS 主循環
	RunService.Heartbeat:Connect(function(deltaTime)
		for _, system in pairs(systems) do
			system(world, deltaTime)
		end
	end)
	
	print("🔄 ECS Systems running:", #systems)
	
end):catch(function(err)
	warn("❌ Server startup failed:", err)
end)

-- 導出 world 供其他模組使用
_G.MatterWorld = world
