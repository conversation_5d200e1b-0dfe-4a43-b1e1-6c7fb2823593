--[[
	FinalGachaTest.lua - 最終抽卡測試
	測試修復後的十連抽功能
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🎯 Final Gacha Test Starting...")

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started")
	
	-- 等待服務初始化
	task.wait(2)
	
	-- 獲取服務
	local GachaService = Knit.GetService("GachaService")
	
	if not GachaService then
		warn("❌ GachaService not found!")
		return
	end
	
	print("✅ GachaService found")
	
	-- 檢查 ProfileService
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	local profile = PlayerProfile.getProfile(player)
	
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Player profile found")
	print("  Current coins:", profile.Data.coins)
	
	-- 確保有足夠金幣
	if profile.Data.coins < 1000 then
		print("💰 Adding coins for test...")
		PlayerProfile.addCoins(player, 2000)
		print("  New balance:", profile.Data.coins)
	end
	
	-- 監聽結果
	local testResults = {
		singlePull = false,
		tenPull = false,
		dataRequest = false,
	}
	
	-- 監聽抽卡結果
	GachaService.GachaResult:Connect(function(results, poolType, isTenPull)
		if isTenPull then
			testResults.tenPull = true
			print("🎉 TEN PULL SUCCESS!")
			print("  Results:", #results)
			for i, result in ipairs(results) do
				print("  " .. i .. ":", result.name, "(" .. result.rarity .. ")")
			end
		else
			testResults.singlePull = true
			print("✅ Single pull success:", results[1].name)
		end
	end)
	
	-- 監聽數據更新
	GachaService.GachaDataUpdated:Connect(function(data)
		testResults.dataRequest = true
		print("✅ Gacha data received")
		print("  History count:", #(data.history or {}))
	end)
	
	-- 監聽金幣不足
	GachaService.InsufficientFunds:Connect(function(required, current)
		warn("❌ Insufficient funds:", required, "required,", current, "current")
	end)
	
	-- 測試序列
	print("\n🧪 Test 1: Request gacha data...")
	GachaService.GetGachaData:Fire()
	
	task.wait(3)
	
	print("\n🧪 Test 2: Single pull...")
	GachaService.SinglePull:Fire("pet")
	
	task.wait(3)
	
	print("\n🧪 Test 3: Ten pull...")
	GachaService.TenPull:Fire("pet")
	
	task.wait(5)
	
	-- 結果總結
	print("\n" .. string.rep("=", 50))
	print("🧪 FINAL TEST RESULTS:")
	print(string.rep("=", 50))
	
	print("Data Request:", testResults.dataRequest and "✅ PASS" or "❌ FAIL")
	print("Single Pull:", testResults.singlePull and "✅ PASS" or "❌ FAIL")
	print("Ten Pull:", testResults.tenPull and "✅ PASS" or "❌ FAIL")
	
	local allPassed = testResults.dataRequest and testResults.singlePull and testResults.tenPull
	
	if allPassed then
		print("\n🎉 ALL TESTS PASSED! Gacha system is working correctly!")
	else
		print("\n❌ Some tests failed. Check the issues above.")
	end
	
	print(string.rep("=", 50))
	
end):catch(function(err)
	warn("❌ Test failed with error:", err)
end)

return true
