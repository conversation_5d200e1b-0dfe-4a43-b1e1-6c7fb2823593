--[[
	PetService.lua - 寵物管理服務（重構版）
	整合 Matter ECS 的寵物管理系統
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

-- ECS 組件
local PositionComponent = require(game:GetService("ReplicatedStorage").ECS.Components.PositionComponent)
local HealthComponent = require(game:GetService("ReplicatedStorage").ECS.Components.HealthComponent)
local DamageComponent = require(game:GetService("ReplicatedStorage").ECS.Components.DamageComponent)
local PetComponent = require(game:GetService("ReplicatedStorage").ECS.Components.PetComponent)
local FollowTargetComponent = require(game:GetService("ReplicatedStorage").ECS.Components.FollowTargetComponent)

-- 資料模組
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.PetDatabase)

local PetService = Knit.CreateService({
	Name = "PetService",
	Client = {
		-- 客戶端可調用的方法
		SummonPet = Knit.CreateSignal(),
		RecallPet = Knit.CreateSignal(),
		GetPetDex = Knit.CreateSignal(),
		PetSummoned = Knit.CreateSignal(),
		PetRecalled = Knit.CreateSignal(),
		PetLevelUp = Knit.CreateSignal(),
		PetDataUpdated = Knit.CreateSignal(),
	},
})

-- 私有變量
local activePetEntities = {} -- 當前召喚的寵物實體 {[player] = entityId}
local petModels = {} -- 寵物模型映射 {[entityId] = model}

function PetService:KnitStart()
	print("🐾 PetService started (ECS Version)")
	
	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found!")
		return
	end
	
	-- 監聽客戶端請求
	self.Client.SummonPet:Connect(function(player, petId)
		self:_summonPet(player, petId)
	end)
	
	self.Client.RecallPet:Connect(function(player)
		self:_recallPet(player)
	end)
	
	self.Client.GetPetDex:Connect(function(player)
		self:_sendPetDex(player)
	end)
	
	-- 監聽玩家離開事件
	Players.PlayerRemoving:Connect(function(player)
		self:_cleanupPlayerPets(player)
	end)
end

function PetService:KnitInit()
	-- 獲取其他服務
	self.PlayerService = Knit.GetService("PlayerService")
end

-- 召喚寵物
function PetService:_summonPet(player, petId)
	-- 檢查玩家是否擁有這隻寵物
	local profile = self.PlayerService:GetPlayerProfile(player)
	if not profile or not profile.Data.ownedPets[petId] then
		warn("Player", player.Name, "doesn't own pet:", petId)
		return
	end
	
	-- 防止重複召喚
	if activePetEntities[player] then
		print("🐾 Pet already active for", player.Name, "- recalling first")
		self:_recallPet(player)
		wait(0.1)
	end
	
	-- 創建寵物實體
	local entityId = self:_createPetEntity(petId, player, profile.Data.ownedPets[petId])
	if entityId then
		activePetEntities[player] = entityId
		
		-- 創建寵物模型
		local petModel = self:_createPetModel(petId, player, entityId)
		if petModel then
			petModels[entityId] = petModel
		end
		
		-- 更新玩家數據
		profile.Data.activePet = petId
		
		-- 通知客戶端
		self.Client.PetSummoned:Fire(player, petId)
		print("🐾 Pet summoned:", petId, "for", player.Name)
	end
end

-- 收回寵物
function PetService:_recallPet(player)
	local entityId = activePetEntities[player]
	if entityId then
		-- 移除寵物實體
		if self.world then
			self.world:despawn(entityId)
		end
		
		-- 銷毀寵物模型
		local petModel = petModels[entityId]
		if petModel then
			petModel:Destroy()
			petModels[entityId] = nil
		end
		
		activePetEntities[player] = nil
		
		-- 更新玩家數據
		local profile = self.PlayerService:GetPlayerProfile(player)
		if profile then
			profile.Data.activePet = nil
		end
		
		-- 通知客戶端
		self.Client.PetRecalled:Fire(player)
		print("🐾 Pet recalled for", player.Name)
	end
end

-- 創建寵物實體
function PetService:_createPetEntity(petId, owner, petData)
	local petConfig = PetDatabase.getPet(petId)
	if not petConfig then
		warn("Pet config not found:", petId)
		return nil
	end
	
	-- 獲取主人位置
	local ownerEntityId = self.PlayerService:GetPlayerEntityId(owner)
	local ownerPosition = Vector3.new(0, 10, 0) -- 默認位置
	
	if ownerEntityId then
		local ownerPos = self.world:get(ownerEntityId, PositionComponent)
		if ownerPos then
			ownerPosition = ownerPos.position + Vector3.new(3, 2, 0)
		end
	end
	
	-- 計算寵物等級後的屬性
	local stats = PetDatabase.calculateStats(petId, petData.level or 1)
	
	-- 創建寵物實體
	local entityId = self.world:spawn(
		PetComponent({
			petId = petId,
			ownerId = owner.UserId,
			level = petData.level or 1,
			experience = petData.experience or 0,
			experienceToNext = (petData.level or 1) * 100,
			rarity = petConfig.rarity,
			isActive = true,
			summonTime = tick(),
		}),
		PositionComponent({
			position = ownerPosition,
			rotation = CFrame.new(ownerPosition),
			lastPosition = ownerPosition,
			velocity = Vector3.new(),
		}),
		HealthComponent({
			current = stats.health,
			maximum = stats.health,
			regeneration = 0.5,
			lastDamageTime = 0,
			isDead = false,
		}),
		DamageComponent({
			attack = stats.attack,
			defense = stats.defense,
			criticalChance = 0.05,
			criticalMultiplier = 1.5,
			elementalType = "None",
			elementalDamage = 0,
		}),
		FollowTargetComponent({
			targetId = ownerEntityId or 0,
			followDistance = 5,
			speed = stats.speed or 14,
			isFollowing = true,
			maxDistance = 50,
			lastTargetPosition = ownerPosition,
		})
	)
	
	print("🐾 Created pet entity:", entityId, "for", owner.Name)
	return entityId
end

-- 創建寵物模型
function PetService:_createPetModel(petId, owner, entityId)
	local petConfig = PetDatabase.getPet(petId)
	if not petConfig then return nil end
	
	-- 創建寵物模型
	local petModel = Instance.new("Model")
	petModel.Name = "Pet_" .. entityId
	petModel.Parent = workspace
	
	-- 創建主體部分
	local body = Instance.new("Part")
	body.Name = "Body"
	body.Size = petConfig.appearance.size
	body.Color = petConfig.appearance.primaryColor
	body.Material = Enum.Material.Neon
	body.Shape = Enum.PartType.Block
	body.TopSurface = Enum.SurfaceType.Smooth
	body.BottomSurface = Enum.SurfaceType.Smooth
	body.CanCollide = false
	body.Parent = petModel
	
	-- 設置為主要部件
	petModel.PrimaryPart = body
	
	-- 添加圓角
	if petConfig.appearance.shape == "Sphere" then
		local corner = Instance.new("SpecialMesh")
		corner.MeshType = Enum.MeshType.Sphere
		corner.Parent = body
	end
	
	-- 添加身體物理
	local bodyVelocity = Instance.new("BodyVelocity")
	bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
	bodyVelocity.Velocity = Vector3.new()
	bodyVelocity.Parent = body
	
	-- 添加寵物標籤
	local ownerValue = Instance.new("ObjectValue")
	ownerValue.Name = "Owner"
	ownerValue.Value = owner
	ownerValue.Parent = petModel
	
	local petIdValue = Instance.new("StringValue")
	petIdValue.Name = "PetId"
	petIdValue.Value = petId
	petIdValue.Parent = petModel
	
	local entityIdValue = Instance.new("IntValue")
	entityIdValue.Name = "EntityId"
	entityIdValue.Value = entityId
	entityIdValue.Parent = petModel
	
	-- 設置初始位置
	local positionComponent = self.world:get(entityId, PositionComponent)
	if positionComponent then
		petModel:SetPrimaryPartCFrame(positionComponent.rotation)
	end
	
	print("🐾 Pet model created:", petConfig.name, "for", owner.Name)
	return petModel
end

-- 發送寵物圖鑑數據
function PetService:_sendPetDex(player)
	local profile = self.PlayerService:GetPlayerProfile(player)
	if profile then
		self.Client.GetPetDex:Fire(player, profile.Data.ownedPets, profile.Data.petDex)
	end
end

-- 玩家離開時清理寵物
function PetService:_cleanupPlayerPets(player)
	self:_recallPet(player)
end

-- 獲取活躍寵物實體ID
function PetService:GetActivePetEntityId(player)
	return activePetEntities[player]
end

-- 客戶端請求召喚寵物
function PetService.Client:SummonPet(player, petId)
	PetService:_summonPet(player, petId)
end

-- 客戶端請求收回寵物
function PetService.Client:RecallPet(player)
	PetService:_recallPet(player)
end

-- 客戶端請求寵物圖鑑
function PetService.Client:GetPetDex(player)
	PetService:_sendPetDex(player)
end

return PetService
