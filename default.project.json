{"name": "pet-rpg", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "Packages": {"$path": "Packages"}, "Shared": {"$path": "src/shared"}, "ECS": {"$path": "src/ECS"}, "DevPackages": {"$path": "DevPackages"}}, "ServerScriptService": {"$className": "ServerScriptService", "Server": {"$path": "src/server"}}, "ServerStorage": {"$className": "ServerStorage", "Packages": {"$path": "ServerPackages"}, "Test": {"$path": "src/Test"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "Client": {"$path": "src/Client"}}}, "Workspace": {"$className": "Workspace", "$properties": {"FilteringEnabled": true}}, "Lighting": {"$className": "Lighting", "$properties": {"Ambient": [0.5, 0.5, 0.5], "Brightness": 1, "ColorShift_Bottom": [0, 0, 0], "ColorShift_Top": [0, 0, 0], "EnvironmentDiffuseScale": 0.25, "EnvironmentSpecularScale": 0.25, "GlobalShadows": true, "OutdoorAmbient": [0.5, 0.5, 0.5], "ShadowSoftness": 0.2, "ClockTime": 14, "GeographicLatitude": 41.733, "TimeOfDay": "14:00:00"}}, "SoundService": {"$className": "SoundService", "$properties": {"RespectFilteringEnabled": true}}}}