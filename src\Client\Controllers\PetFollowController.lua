--[[
	PetFollowController - 寵物跟隨控制器
	處理寵物跟隨玩家的邏輯和動畫
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")
local PetConfig = require(game:GetService("ReplicatedStorage").Shared.PetConfig)

local PetFollowController = Knit.CreateController({
	Name = "PetFollowController",
})

-- 私有變量
local player = Players.LocalPlayer
local followingPets = {} -- 正在跟隨的寵物
local followConnection
local floatOffsets = {} -- 為每個寵物存儲漂浮時間偏移

function PetFollowController:KnitStart()
	print("🐾 PetFollowController started")
	
	-- 監聽寵物召喚/收回事件
	local PetService = Knit.GetService("PetService")
	
	PetService.PetSummoned:Connect(function(petId)
		-- 事件已經只發送給對應的玩家，所以直接開始跟隨
		self:_startFollowing(petId)
	end)

	PetService.PetRecalled:Connect(function()
		-- 事件已經只發送給對應的玩家，所以直接停止跟隨
		self:_stopFollowing()
	end)

	-- 等待並獲取戰鬥服務
	Knit.OnStart():andThen(function()
		self.CombatService = Knit.GetService("CombatService")
	end)
end

function PetFollowController:KnitInit()
	-- 初始化控制器依賴
end

-- 開始跟隨
function PetFollowController:_startFollowing(petId)
	-- 等待寵物模型出現
	local petModel = self:_waitForPetModel(petId)
	if not petModel then
		warn("Pet model not found for:", petId)
		return
	end

	followingPets[petId] = petModel

	-- 為每個寵物設置隨機的漂浮時間偏移
	floatOffsets[petId] = math.random() * math.pi * 2

	-- 添加模型刪除監聽
	petModel.AncestryChanged:Connect(function()
		if not petModel.Parent then
			print("⚠️ Pet model", petId, "was removed from workspace")
			followingPets[petId] = nil
			floatOffsets[petId] = nil
		end
	end)

	-- 開始跟隨循環
	if not followConnection then
		followConnection = RunService.Heartbeat:Connect(function()
			self:_updatePetFollow()
		end)
	end

	print("🐾 Started following pet:", petId, "at position:", petModel.PrimaryPart.Position)
end

-- 停止跟隨
function PetFollowController:_stopFollowing()
	-- 清理所有寵物的 BodyPosition
	for petId, petModel in pairs(followingPets) do
		if petModel and petModel:FindFirstChild("HumanoidRootPart") then
			local bodyPosition = petModel.HumanoidRootPart:FindFirstChild("BodyPosition")
			if bodyPosition then
				bodyPosition:Destroy()
			end
		end
	end

	followingPets = {}
	floatOffsets = {}

	if followConnection then
		followConnection:Disconnect()
		followConnection = nil
	end

	print("🐾 Stopped following pets")
end

-- 等待寵物模型出現
function PetFollowController:_waitForPetModel(petId)
	local maxWait = 5 -- 最多等待5秒
	local waited = 0
	
	while waited < maxWait do
		-- 在workspace中尋找寵物模型
		for _, model in pairs(workspace:GetChildren()) do
			if model:IsA("Model") and model:FindFirstChild("PetId") then
				local petIdValue = model:FindFirstChild("PetId")
				local ownerValue = model:FindFirstChild("Owner")
				
				if petIdValue.Value == petId and ownerValue.Value == player then
					return model
				end
			end
		end
		
		wait(0.1)
		waited = waited + 0.1
	end
	
	return nil
end

-- 更新寵物跟隨
function PetFollowController:_updatePetFollow()
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return
	end

	local playerRoot = player.Character.HumanoidRootPart
	local playerPos = playerRoot.Position

	for petId, petModel in pairs(followingPets) do
		if petModel and petModel.Parent and petModel:FindFirstChild("HumanoidRootPart") then
			self:_updateSinglePetFollow(petId, petModel, playerPos)
			-- 檢查寵物戰鬥
			self:_updatePetCombat(petId, petModel)
		else
			-- 寵物模型已被刪除，清理引用
			followingPets[petId] = nil
			print("🐾 Pet model removed:", petId)
		end
	end
end

-- 更新單個寵物跟隨
function PetFollowController:_updateSinglePetFollow(petId, petModel, playerPos)
	local petConfig = PetConfig.getPet(petId)
	if not petConfig then return end

	local petRoot = petModel:FindFirstChild("HumanoidRootPart")
	local petHumanoid = petModel:FindFirstChild("Humanoid")

	if not petRoot or not petHumanoid then return end

	local petPos = petRoot.Position
	local followDistance = petConfig.followSettings.distance or 5
	local moveSpeed = petConfig.followSettings.speed or 16

	-- 計算到玩家的距離
	local distance = (playerPos - petPos).Magnitude

	-- 如果距離太遠（超過25格），直接傳送到玩家身邊
	if distance > 25 then
		local teleportPos = playerPos + Vector3.new(
			math.random(-3, 3),
			2, -- 抬高一點避免卡在地面
			math.random(-3, 3)
		)

		petModel:SetPrimaryPartCFrame(CFrame.new(teleportPos))
		print("🐾 Teleported pet", petId, "to player (was too far:", math.floor(distance), ")")
		return -- 傳送後直接返回，不執行其他邏輯
	end

	-- 計算基礎目標位置
	local direction = (playerPos - petPos).Unit
	local baseTargetPos = playerPos - direction * (followDistance - 1)

	-- 檢查是否為飛行寵物
	local canFly = petConfig.followSettings.canFly or false
	if canFly then
		-- 飛行寵物在玩家頭頂飛行
		baseTargetPos = baseTargetPos + Vector3.new(0, 5, 0)
	end

	-- 添加漂浮效果
	local currentTime = tick()
	local floatOffset = floatOffsets[petId] or 0
	local floatAmplitude = canFly and 1.5 or 0.5 -- 飛行寵物漂浮幅度更大
	local floatSpeed = canFly and 2 or 1.5 -- 飛行寵物漂浮速度更快
	local floatHeight = math.sin(currentTime * floatSpeed + floatOffset) * floatAmplitude

	local finalTargetPos = baseTargetPos + Vector3.new(0, floatHeight, 0)

	-- 使用 BodyPosition 進行漂浮移動
	local bodyPosition = petRoot:FindFirstChild("BodyPosition")
	if not bodyPosition then
		bodyPosition = Instance.new("BodyPosition")
		bodyPosition.MaxForce = Vector3.new(4000, 4000, 4000)
		bodyPosition.P = canFly and 3000 or 2000 -- 飛行寵物更敏感
		bodyPosition.D = canFly and 500 or 800 -- 飛行寵物阻尼更小，更飄逸
		bodyPosition.Parent = petRoot
	end

	-- 根據距離決定移動策略
	if distance > followDistance then
		-- 需要移動時設置目標位置
		bodyPosition.Position = finalTargetPos

		-- 讓寵物面向移動方向
		local moveDirection = (baseTargetPos - petPos).Unit
		local lookDirection = Vector3.new(moveDirection.X, 0, moveDirection.Z)
		if lookDirection.Magnitude > 0 then
			petRoot.CFrame = CFrame.lookAt(petRoot.Position, petRoot.Position + lookDirection)
		end
	else
		-- 距離合適時，只保持漂浮效果
		local currentPos = petRoot.Position
		local floatOnlyPos = Vector3.new(currentPos.X, baseTargetPos.Y + floatHeight, currentPos.Z)
		bodyPosition.Position = floatOnlyPos
	end
end

-- 播放閒置動畫（現在由漂浮系統處理，保留作為備用）
function PetFollowController:_playIdleAnimation(petModel, petConfig)
	local body = petModel:FindFirstChild("Body")
	if not body then return end

	-- 檢查是否為飛行寵物
	local canFly = petConfig.followSettings.canFly or false

	if canFly then
		-- 飛行寵物的輕微旋轉動畫
		local TweenService = game:GetService("TweenService")
		local originalCFrame = body.CFrame

		local rotateLeft = TweenService:Create(
			body,
			TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{CFrame = originalCFrame * CFrame.Angles(0, math.rad(10), math.rad(5))}
		)

		local rotateRight = TweenService:Create(
			body,
			TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{CFrame = originalCFrame * CFrame.Angles(0, math.rad(-10), math.rad(-5))}
		)

		rotateLeft:Play()
		rotateLeft.Completed:Connect(function()
			rotateRight:Play()
			rotateRight.Completed:Connect(function()
				-- 循環動畫
				if math.random() < 0.3 then
					self:_playIdleAnimation(petModel, petConfig)
				end
			end)
		end)
	else
		-- 地面寵物的輕微跳躍（由於已有漂浮效果，減少幅度）
		local TweenService = game:GetService("TweenService")
		local originalCFrame = body.CFrame

		local jumpUp = TweenService:Create(
			body,
			TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{CFrame = originalCFrame + Vector3.new(0, 0.5, 0)}
		)

		local jumpDown = TweenService:Create(
			body,
			TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
			{CFrame = originalCFrame}
		)

		jumpUp:Play()
		jumpUp.Completed:Connect(function()
			jumpDown:Play()
		end)
	end
end

-- 更新寵物戰鬥
function PetFollowController:_updatePetCombat(petId, petModel)
	if not self.CombatService then
		print("🐾 CombatService not available for pet combat")
		return
	end

	local petPos = petModel.HumanoidRootPart.Position
	local nearestMonster = nil
	local nearestDistance = math.huge

	-- 尋找最近的怪物
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") and model:FindFirstChild("HumanoidRootPart") then
			local distance = (model.HumanoidRootPart.Position - petPos).Magnitude
			if distance < nearestDistance and distance <= 15 then -- 寵物戰鬥範圍15格
				nearestDistance = distance
				-- 從模型名稱中提取 instanceId（格式：monsterId_instanceId）
				local parts = string.split(model.Name, "_")
				if #parts >= 2 then
					nearestMonster = parts[2] -- instanceId
				end
			end
		end
	end

	-- 如果找到怪物且在攻擊範圍內，讓寵物攻擊
	if nearestMonster and nearestDistance <= 8 and self.CombatService then
		-- 檢查攻擊冷卻（簡單實現）
		local currentTime = tick()
		local lastAttackKey = petId .. "_lastAttack"
		local lastTargetKey = petId .. "_lastTarget"
		if not self[lastAttackKey] then
			self[lastAttackKey] = 0
		end

		-- 只在新目標或攻擊時輸出調試信息
		if self[lastTargetKey] ~= nearestMonster then
			print("🐾 Pet", petId, "found new target:", nearestMonster, "distance:", nearestDistance)
			self[lastTargetKey] = nearestMonster
		end

		if currentTime - self[lastAttackKey] >= 2 then -- 2秒攻擊冷卻
			print("🐾 Pet", petId, "attacking monster", nearestMonster)
			self.CombatService:PetAttackMonster(petId, nearestMonster)
			self[lastAttackKey] = currentTime

			-- 寵物攻擊動畫
			self:_playPetAttackAnimation(petModel, nearestMonster)
		end
	else
		-- 清理目標記錄
		local lastTargetKey = petId .. "_lastTarget"
		if self[lastTargetKey] then
			self[lastTargetKey] = nil
		end
	end
end

-- 獲取當前跟隨的寵物
function PetFollowController:GetFollowingPets()
	return followingPets
end

-- 寵物攻擊動畫
function PetFollowController:_playPetAttackAnimation(petModel, targetMonsterId)
	if not petModel or not petModel:FindFirstChild("HumanoidRootPart") then
		return
	end

	local rootPart = petModel.HumanoidRootPart
	local TweenService = game:GetService("TweenService")

	-- 尋找目標怪物位置
	local targetPosition = nil
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, targetMonsterId) and model:FindFirstChild("HumanoidRootPart") then
			targetPosition = model.HumanoidRootPart.Position
			break
		end
	end

	if targetPosition then
		-- 轉向目標
		local direction = (targetPosition - rootPart.Position).Unit
		local lookAtCFrame = CFrame.lookAt(rootPart.Position, targetPosition)

		local turnTween = TweenService:Create(
			rootPart,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad),
			{CFrame = lookAtCFrame}
		)
		turnTween:Play()

		turnTween.Completed:Connect(function()
			-- 根據寵物類型使用不同的攻擊方式
			local petName = petModel.Name
			if string.find(petName, "fire") then
				self:_performFireAttack(rootPart, targetPosition)
			elseif string.find(petName, "water") then
				self:_performWaterAttack(rootPart, targetPosition)
			elseif string.find(petName, "earth") then
				self:_performEarthAttack(rootPart, targetPosition)
			else
				self:_performGenericAttack(rootPart, targetPosition)
			end
		end)
	end
end

-- 火系攻擊
function PetFollowController:_performFireAttack(rootPart, targetPosition)
	print("🔥 Pet fire attack!")
	local TweenService = game:GetService("TweenService")

	-- 創建火球
	local fireball = Instance.new("Part")
	fireball.Name = "Fireball"
	fireball.Size = Vector3.new(1, 1, 1)
	fireball.Shape = Enum.PartType.Ball
	fireball.Material = Enum.Material.Neon
	fireball.BrickColor = BrickColor.new("Bright red")
	fireball.CanCollide = false
	fireball.Anchored = true
	fireball.CFrame = CFrame.new(rootPart.Position + Vector3.new(0, 1, 0))
	fireball.Parent = workspace

	-- 火球飛向目標
	local fireballTween = TweenService:Create(
		fireball,
		TweenInfo.new(0.5, Enum.EasingStyle.Quad),
		{CFrame = CFrame.new(targetPosition + Vector3.new(0, 1, 0))}
	)

	fireballTween:Play()
	fireballTween.Completed:Connect(function()
		-- 火爆炸效果
		local explosion = Instance.new("Explosion")
		explosion.Position = targetPosition
		explosion.BlastRadius = 6
		explosion.BlastPressure = 0
		explosion.Parent = workspace

		fireball:Destroy()
	end)
end

-- 水系攻擊
function PetFollowController:_performWaterAttack(rootPart, targetPosition)
	print("💧 Pet water attack!")
	local TweenService = game:GetService("TweenService")

	-- 創建水球
	local waterball = Instance.new("Part")
	waterball.Name = "Waterball"
	waterball.Size = Vector3.new(1.2, 1.2, 1.2)
	waterball.Shape = Enum.PartType.Ball
	waterball.Material = Enum.Material.ForceField
	waterball.BrickColor = BrickColor.new("Bright blue")
	waterball.CanCollide = false
	waterball.Anchored = true
	waterball.Transparency = 0.3
	waterball.CFrame = CFrame.new(rootPart.Position + Vector3.new(0, 1, 0))
	waterball.Parent = workspace

	-- 水球飛向目標
	local waterballTween = TweenService:Create(
		waterball,
		TweenInfo.new(0.4, Enum.EasingStyle.Sine),
		{CFrame = CFrame.new(targetPosition + Vector3.new(0, 1, 0))}
	)

	waterballTween:Play()
	waterballTween.Completed:Connect(function()
		-- 水花效果
		for i = 1, 5 do
			local splash = Instance.new("Part")
			splash.Size = Vector3.new(0.5, 0.5, 0.5)
			splash.Material = Enum.Material.Neon
			splash.BrickColor = BrickColor.new("Cyan")
			splash.CanCollide = false
			splash.Anchored = true
			splash.CFrame = CFrame.new(targetPosition + Vector3.new(
				math.random(-2, 2),
				math.random(0, 3),
				math.random(-2, 2)
			))
			splash.Parent = workspace

			-- 水花淡出
			local splashTween = TweenService:Create(
				splash,
				TweenInfo.new(1, Enum.EasingStyle.Quad),
				{Transparency = 1, Size = Vector3.new(0.1, 0.1, 0.1)}
			)
			splashTween:Play()
			splashTween.Completed:Connect(function()
				splash:Destroy()
			end)
		end

		waterball:Destroy()
	end)
end

-- 土系攻擊
function PetFollowController:_performEarthAttack(rootPart, targetPosition)
	print("🌍 Pet earth attack!")
	local TweenService = game:GetService("TweenService")

	-- 創建石柱從地面升起
	local rockPillar = Instance.new("Part")
	rockPillar.Name = "RockPillar"
	rockPillar.Size = Vector3.new(2, 1, 2)
	rockPillar.Material = Enum.Material.Rock
	rockPillar.BrickColor = BrickColor.new("Dark stone grey")
	rockPillar.CanCollide = false
	rockPillar.Anchored = true
	rockPillar.CFrame = CFrame.new(targetPosition.X, targetPosition.Y - 2, targetPosition.Z)
	rockPillar.Parent = workspace

	-- 石柱升起
	local riseTween = TweenService:Create(
		rockPillar,
		TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size = Vector3.new(2, 6, 2),
			CFrame = CFrame.new(targetPosition.X, targetPosition.Y + 1, targetPosition.Z)
		}
	)

	riseTween:Play()
	riseTween.Completed:Connect(function()
		wait(0.5)
		-- 石柱下沉消失
		local sinkTween = TweenService:Create(
			rockPillar,
			TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
			{
				Size = Vector3.new(2, 0.1, 2),
				CFrame = CFrame.new(targetPosition.X, targetPosition.Y - 3, targetPosition.Z)
			}
		)
		sinkTween:Play()
		sinkTween.Completed:Connect(function()
			rockPillar:Destroy()
		end)
	end)
end

-- 通用攻擊
function PetFollowController:_performGenericAttack(rootPart, targetPosition)
	print("✨ Pet magic attack!")

	-- 創建魔法彈
	local magicBall = Instance.new("Part")
	magicBall.Name = "MagicBall"
	magicBall.Size = Vector3.new(0.8, 0.8, 0.8)
	magicBall.Shape = Enum.PartType.Ball
	magicBall.Material = Enum.Material.Neon
	magicBall.BrickColor = BrickColor.new("Bright violet")
	magicBall.CanCollide = false
	magicBall.Anchored = true
	magicBall.CFrame = CFrame.new(rootPart.Position + Vector3.new(0, 1, 0))
	magicBall.Parent = workspace

	-- 魔法彈飛向目標
	local TweenService = game:GetService("TweenService")
	local magicTween = TweenService:Create(
		magicBall,
		TweenInfo.new(0.4, Enum.EasingStyle.Sine),
		{CFrame = CFrame.new(targetPosition + Vector3.new(0, 1, 0))}
	)

	magicTween:Play()
	magicTween.Completed:Connect(function()
		-- 魔法爆炸
		local explosion = Instance.new("Explosion")
		explosion.Position = targetPosition
		explosion.BlastRadius = 5
		explosion.BlastPressure = 0
		explosion.Parent = workspace

		magicBall:Destroy()
	end)
end

return PetFollowController
