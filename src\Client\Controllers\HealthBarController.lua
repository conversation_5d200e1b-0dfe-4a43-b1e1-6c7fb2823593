--[[
	HealthBarController - 血量條控制器
	管理客戶端血量條的顯示和設置
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local MonsterHealthUI = require(game:GetService("ReplicatedStorage").Shared.MonsterHealthUI)

local HealthBarController = Knit.CreateController({
	Name = "HealthBarController",
})

-- 私有變量
local player = Players.LocalPlayer
local settings = {
	showHealthBars = true,
	showHealthText = true,
	healthBarDistance = 50, -- 顯示血量條的最大距離
	updateInterval = 0.1 -- 更新間隔
}

local lastUpdateTime = 0

function HealthBarController:KnitStart()
	print("💚 HealthBarController started")
	
	-- 監聽設置變化
	self:_setupSettingsUI()
	
	-- 定期更新血量條可見性
	RunService.Heartbeat:Connect(function()
		self:_updateHealthBarVisibility()
	end)
	
	-- 監聽怪物生成事件
	Knit.OnStart():andThen(function()
		local MonsterService = Knit.GetService("MonsterService")
		if MonsterService then
			MonsterService.MonsterSpawned:Connect(function(instanceId, monsterId, position)
				self:_onMonsterSpawned(instanceId, monsterId, position)
			end)
		end
	end)
end

-- 怪物生成事件處理
function HealthBarController:_onMonsterSpawned(instanceId, monsterId, position)
	-- 等待怪物模型完全載入
	task.wait(0.1)
	
	-- 尋找新生成的怪物
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, instanceId) then
			local humanoid = model:FindFirstChild("Humanoid")
			if humanoid then
				-- 確保血量條存在
				if not model:FindFirstChild("HealthBarGui") then
					MonsterHealthUI.createHealthBar(model, humanoid.MaxHealth, humanoid.Health)
				end
				
				-- 設置初始可見性
				MonsterHealthUI.setHealthBarVisible(model, settings.showHealthBars)
			end
			break
		end
	end
end

-- 更新血量條可見性
function HealthBarController:_updateHealthBarVisibility()
	local currentTime = tick()
	if currentTime - lastUpdateTime < settings.updateInterval then
		return
	end
	lastUpdateTime = currentTime
	
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return
	end
	
	local playerPosition = player.Character.HumanoidRootPart.Position
	
	-- 遍歷所有怪物
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") and model:FindFirstChild("HumanoidRootPart") then
			local distance = (model.HumanoidRootPart.Position - playerPosition).Magnitude
			local shouldShow = settings.showHealthBars and distance <= settings.healthBarDistance
			
			MonsterHealthUI.setHealthBarVisible(model, shouldShow)
		end
	end
end

-- 設置血量條顯示
function HealthBarController:setHealthBarsVisible(visible)
	settings.showHealthBars = visible
	
	-- 立即更新所有血量條
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") then
			MonsterHealthUI.setHealthBarVisible(model, visible)
		end
	end
	
	print("💚 Health bars visibility set to:", visible)
end

-- 設置血量條顯示距離
function HealthBarController:setHealthBarDistance(distance)
	settings.healthBarDistance = math.max(10, distance)
	print("💚 Health bar distance set to:", settings.healthBarDistance)
end

-- 設置血量文字顯示
function HealthBarController:setHealthTextVisible(visible)
	settings.showHealthText = visible
	
	-- 更新所有血量條的文字顯示
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("HealthBarGui") then
			local healthBarGui = model.HealthBarGui
			local healthText = healthBarGui:FindFirstChild("Background") and 
							   healthBarGui.Background:FindFirstChild("HealthText")
			if healthText then
				healthText.Visible = visible
			end
		end
	end
	
	print("💚 Health text visibility set to:", visible)
end

-- 創建設置UI
function HealthBarController:_setupSettingsUI()
	-- 這裡可以添加設置UI，暫時使用命令行
	-- 玩家可以通過聊天命令來調整設置
	
	player.Chatted:Connect(function(message)
		local args = string.split(message:lower(), " ")
		
		if args[1] == "/healthbars" then
			if args[2] == "on" then
				self:setHealthBarsVisible(true)
			elseif args[2] == "off" then
				self:setHealthBarsVisible(false)
			elseif args[2] == "distance" and tonumber(args[3]) then
				self:setHealthBarDistance(tonumber(args[3]))
			elseif args[2] == "text" then
				if args[3] == "on" then
					self:setHealthTextVisible(true)
				elseif args[3] == "off" then
					self:setHealthTextVisible(false)
				end
			else
				self:_showHealthBarHelp()
			end
		end
	end)
end

-- 顯示血量條命令幫助
function HealthBarController:_showHealthBarHelp()
	local helpText = {
		"💚 血量條命令幫助:",
		"/healthbars on - 顯示血量條",
		"/healthbars off - 隱藏血量條", 
		"/healthbars distance [數字] - 設置顯示距離",
		"/healthbars text on - 顯示血量文字",
		"/healthbars text off - 隱藏血量文字"
	}
	
	for _, text in ipairs(helpText) do
		print(text)
	end
end

-- 獲取當前設置
function HealthBarController:getSettings()
	return {
		showHealthBars = settings.showHealthBars,
		showHealthText = settings.showHealthText,
		healthBarDistance = settings.healthBarDistance,
		updateInterval = settings.updateInterval
	}
end

-- 重置所有血量條
function HealthBarController:refreshAllHealthBars()
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") and model:FindFirstChild("Humanoid") then
			local humanoid = model.Humanoid
			
			-- 移除舊的血量條
			MonsterHealthUI.removeHealthBar(model)
			
			-- 創建新的血量條
			task.wait(0.1)
			MonsterHealthUI.createHealthBar(model, humanoid.MaxHealth, humanoid.Health)
			MonsterHealthUI.setHealthBarVisible(model, settings.showHealthBars)
		end
	end
	
	print("💚 All health bars refreshed")
end

-- 獲取血量條統計
function HealthBarController:getHealthBarStats()
	local totalMonsters = 0
	local monstersWithHealthBars = 0
	local visibleHealthBars = 0
	
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") then
			totalMonsters = totalMonsters + 1
			
			local healthBarGui = model:FindFirstChild("HealthBarGui")
			if healthBarGui then
				monstersWithHealthBars = monstersWithHealthBars + 1
				if healthBarGui.Enabled then
					visibleHealthBars = visibleHealthBars + 1
				end
			end
		end
	end
	
	return {
		totalMonsters = totalMonsters,
		monstersWithHealthBars = monstersWithHealthBars,
		visibleHealthBars = visibleHealthBars,
		settings = settings
	}
end

return HealthBarController
