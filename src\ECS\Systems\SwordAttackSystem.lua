--[[
	SwordAttackSystem.lua - 劍擊攻擊系統
	處理玩家和寵物的劍擊攻擊邏輯
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local HealthComponent = require(script.Parent.Parent.Components.HealthComponent)
local DamageComponent = require(script.Parent.Parent.Components.DamageComponent)
local SwordSwingComponent = require(script.Parent.Parent.Components.SwordSwingComponent)
local PetComponent = require(script.Parent.Parent.Components.PetComponent)

local function SwordAttackSystem(world, deltaTime)
	-- 查詢所有正在揮劍的實體
	for entityId, swordSwing, position, damage in world:query(SwordSwingComponent, PositionComponent, DamageComponent) do
		if swordSwing.isSwinging then
			local currentTime = tick()
			local swingProgress = (currentTime - swordSwing.swingStartTime) / swordSwing.swingDuration
			
			-- 檢查揮劍是否完成
			if swingProgress >= 1.0 then
				-- 結束揮劍
				world:insert(entityId, swordSwing:patch({
					isSwinging = false,
					swingStartTime = 0,
					hasHit = {}, -- 清空命中記錄
				}))
			else
				-- 在揮劍過程中檢測命中（只在揮劍中期檢測，避免過早或過晚命中）
				if swingProgress >= 0.3 and swingProgress <= 0.7 then
					local swingPosition = position.position
					local swingRange = swordSwing.range
					local swingDamage = swordSwing.damage
					
					-- 查詢範圍內的目標
					for targetId, targetPosition, targetHealth in world:query(PositionComponent, HealthComponent) do
						if targetId ~= entityId and not swordSwing.hasHit[targetId] then -- 不攻擊自己且未命中過
							local distance = (targetPosition.position - swingPosition).Magnitude
							
							if distance <= swingRange then
								-- 檢查是否為有效目標
								local isValidTarget = false
								
								-- 如果攻擊者是玩家，可以攻擊怪物
								local attackerPet = world:get(entityId, PetComponent)
								local targetPet = world:get(targetId, PetComponent)
								
								-- 簡化的目標檢測邏輯
								-- 玩家和寵物可以攻擊怪物
								-- 怪物可以攻擊玩家和寵物
								if not attackerPet and not targetPet then
									-- 玩家攻擊怪物
									isValidTarget = true
								elseif attackerPet and not targetPet then
									-- 寵物攻擊怪物
									isValidTarget = true
								elseif not attackerPet and targetPet then
									-- 怪物攻擊寵物
									isValidTarget = true
								end
								
								if isValidTarget then
									-- 計算傷害
									local targetDamage = world:get(targetId, DamageComponent)
									local finalDamage = math.max(1, swingDamage - (targetDamage and targetDamage.defense or 0))
									
									-- 檢查暴擊
									if damage.criticalChance > 0 and math.random() < damage.criticalChance then
										finalDamage = finalDamage * damage.criticalMultiplier
										print("💥 Critical hit! Damage:", finalDamage)
									end
									
									-- 造成傷害
									local newHealth = math.max(0, targetHealth.current - finalDamage)
									world:insert(targetId, targetHealth:patch({
										current = newHealth,
										lastDamageTime = currentTime,
									}))
									
									-- 記錄已命中此目標
									local newHasHit = {}
									for k, v in pairs(swordSwing.hasHit) do
										newHasHit[k] = v
									end
									newHasHit[targetId] = true
									
									world:insert(entityId, swordSwing:patch({
										hasHit = newHasHit,
									}))
									
									print("⚔️ Sword attack hit! Damage:", finalDamage, "Remaining health:", newHealth)
									
									-- 如果目標死亡
									if newHealth <= 0 then
										world:insert(targetId, targetHealth:patch({
											isDead = true,
										}))
										print("💀 Target defeated!")
									end
								end
							end
						end
					end
				end
			end
		end
	end
end

return SwordAttackSystem
