--[[
	PetDatabase - 寵物資料庫
	包含所有寵物的配置數據和抽卡機率
]]

local PetDatabase = {}

-- 寵物稀有度定義
PetDatabase.Rarities = {
	Common = {
		name = "普通",
		color = Color3.fromRGB(150, 150, 150),
		probability = 0.60, -- 60%
		stars = 1,
	},
	Uncommon = {
		name = "稀有",
		color = Color3.fromRGB(0, 255, 0),
		probability = 0.25, -- 25%
		stars = 2,
	},
	Rare = {
		name = "史詩",
		color = Color3.fromRGB(0, 100, 255),
		probability = 0.12, -- 12%
		stars = 3,
	},
	Epic = {
		name = "傳說",
		color = Color3.fromRGB(128, 0, 255),
		probability = 0.025, -- 2.5%
		stars = 4,
	},
	Legendary = {
		name = "神話",
		color = Color3.fromRGB(255, 215, 0),
		probability = 0.005, -- 0.5%
		stars = 5,
	},
}

-- 寵物配置數據
PetDatabase.Pets = {
	-- 普通寵物
	Slime = {
		id = "Slime",
		name = "史萊姆",
		rarity = "Common",
		description = "一隻可愛的綠色史萊姆，喜歡彈跳。",
		baseStats = {
			health = 80,
			attack = 15,
			defense = 3,
			speed = 12,
		},
		appearance = {
			size = Vector3.new(2, 2, 2),
			primaryColor = Color3.fromRGB(0, 255, 0),
			secondaryColor = Color3.fromRGB(0, 200, 0),
			shape = "Sphere",
		},
		abilities = {
			"Bounce", -- 彈跳攻擊
		},
		levelUpStats = {
			healthPerLevel = 8,
			attackPerLevel = 2,
			defensePerLevel = 1,
		},
	},
	
	Wolf = {
		id = "Wolf",
		name = "灰狼",
		rarity = "Common",
		description = "忠誠的灰狼，擁有敏銳的嗅覺。",
		baseStats = {
			health = 100,
			attack = 18,
			defense = 5,
			speed = 16,
		},
		appearance = {
			size = Vector3.new(3, 2, 4),
			primaryColor = Color3.fromRGB(100, 100, 100),
			secondaryColor = Color3.fromRGB(80, 80, 80),
			shape = "Box",
		},
		abilities = {
			"Howl", -- 嚎叫增益
		},
		levelUpStats = {
			healthPerLevel = 10,
			attackPerLevel = 3,
			defensePerLevel = 1,
		},
	},
	
	-- 稀有寵物
	FireSpirit = {
		id = "FireSpirit",
		name = "火焰精靈",
		rarity = "Uncommon",
		description = "燃燒著熊熊烈火的精靈，攻擊帶有火焰傷害。",
		baseStats = {
			health = 90,
			attack = 25,
			defense = 4,
			speed = 14,
		},
		appearance = {
			size = Vector3.new(2.5, 3, 2.5),
			primaryColor = Color3.fromRGB(255, 100, 0),
			secondaryColor = Color3.fromRGB(255, 200, 0),
			shape = "Sphere",
		},
		abilities = {
			"Fireball", -- 火球攻擊
			"Burn", -- 燃燒效果
		},
		levelUpStats = {
			healthPerLevel = 9,
			attackPerLevel = 4,
			defensePerLevel = 1,
		},
	},
	
	IceWolf = {
		id = "IceWolf",
		name = "冰霜狼",
		rarity = "Uncommon",
		description = "來自極地的冰霜狼，攻擊帶有冰凍效果。",
		baseStats = {
			health = 120,
			attack = 22,
			defense = 8,
			speed = 15,
		},
		appearance = {
			size = Vector3.new(3.5, 2.5, 4.5),
			primaryColor = Color3.fromRGB(150, 200, 255),
			secondaryColor = Color3.fromRGB(100, 150, 255),
			shape = "Box",
		},
		abilities = {
			"IceBreath", -- 冰息攻擊
			"Freeze", -- 冰凍效果
		},
		levelUpStats = {
			healthPerLevel = 12,
			attackPerLevel = 3,
			defensePerLevel = 2,
		},
	},
	
	-- 史詩寵物
	DragonHatchling = {
		id = "DragonHatchling",
		name = "幼龍",
		rarity = "Rare",
		description = "剛孵化的幼龍，擁有巨大的成長潛力。",
		baseStats = {
			health = 150,
			attack = 35,
			defense = 12,
			speed = 18,
		},
		appearance = {
			size = Vector3.new(4, 3, 5),
			primaryColor = Color3.fromRGB(200, 0, 0),
			secondaryColor = Color3.fromRGB(255, 100, 0),
			shape = "Box",
		},
		abilities = {
			"DragonBreath", -- 龍息攻擊
			"Flight", -- 飛行能力
			"Roar", -- 龍吼威嚇
		},
		levelUpStats = {
			healthPerLevel = 15,
			attackPerLevel = 5,
			defensePerLevel = 2,
		},
	},
	
	-- 傳說寵物
	PhoenixChick = {
		id = "PhoenixChick",
		name = "鳳凰雛鳥",
		rarity = "Epic",
		description = "傳說中的鳳凰幼體，擁有重生的力量。",
		baseStats = {
			health = 180,
			attack = 40,
			defense = 15,
			speed = 20,
		},
		appearance = {
			size = Vector3.new(3, 4, 4),
			primaryColor = Color3.fromRGB(255, 50, 50),
			secondaryColor = Color3.fromRGB(255, 200, 0),
			shape = "Sphere",
		},
		abilities = {
			"Phoenix Fire", -- 鳳凰之火
			"Rebirth", -- 重生能力
			"Healing Aura", -- 治療光環
		},
		levelUpStats = {
			healthPerLevel = 18,
			attackPerLevel = 6,
			defensePerLevel = 3,
		},
	},
	
	-- 神話寵物
	CelestialDragon = {
		id = "CelestialDragon",
		name = "天界龍",
		rarity = "Legendary",
		description = "來自天界的神龍，擁有無與倫比的力量。",
		baseStats = {
			health = 250,
			attack = 60,
			defense = 25,
			speed = 25,
		},
		appearance = {
			size = Vector3.new(6, 4, 8),
			primaryColor = Color3.fromRGB(255, 215, 0),
			secondaryColor = Color3.fromRGB(255, 255, 255),
			shape = "Box",
		},
		abilities = {
			"Celestial Beam", -- 天界光束
			"Divine Protection", -- 神聖護盾
			"Time Manipulation", -- 時間操控
			"Cosmic Storm", -- 宇宙風暴
		},
		levelUpStats = {
			healthPerLevel = 25,
			attackPerLevel = 8,
			defensePerLevel = 4,
		},
	},
}

-- 獲取寵物配置
function PetDatabase.getPet(petId)
	return PetDatabase.Pets[petId]
end

-- 獲取所有寵物
function PetDatabase.getAllPets()
	return PetDatabase.Pets
end

-- 根據稀有度獲取寵物列表
function PetDatabase.getPetsByRarity(rarity)
	local pets = {}
	for petId, petData in pairs(PetDatabase.Pets) do
		if petData.rarity == rarity then
			pets[petId] = petData
		end
	end
	return pets
end

-- 獲取稀有度信息
function PetDatabase.getRarity(rarityName)
	return PetDatabase.Rarities[rarityName]
end

-- 計算寵物等級後的屬性
function PetDatabase.calculateStats(petId, level)
	local petConfig = PetDatabase.getPet(petId)
	if not petConfig then return nil end
	
	local stats = {}
	stats.health = petConfig.baseStats.health + (petConfig.levelUpStats.healthPerLevel * (level - 1))
	stats.attack = petConfig.baseStats.attack + (petConfig.levelUpStats.attackPerLevel * (level - 1))
	stats.defense = petConfig.baseStats.defense + (petConfig.levelUpStats.defensePerLevel * (level - 1))
	stats.speed = petConfig.baseStats.speed
	
	return stats
end

return PetDatabase
