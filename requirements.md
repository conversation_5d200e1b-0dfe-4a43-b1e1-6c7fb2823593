# 專案需求文檔

## 專案概述
基於 Roblox 的多人在線戰鬥系統，整合 Knit 框架和 Matter ECS 架構，支援玩家戰鬥、寵物系統、抽卡機制和實時戰鬥動畫。

## 核心功能需求

### 1. 玩家系統
- 玩家登入/登出管理
- 玩家資料載入與保存（ProfileService）
- 玩家狀態管理（血量、攻擊力、防禦力）
- 玩家經驗值與等級系統

### 2. 寵物系統
- 寵物召喚與收回
- 寵物圖鑑系統
- 寵物升級與經驗值
- 寵物自動跟隨與攻擊
- 寵物裝備系統

### 3. 戰鬥系統
- 玩家攻擊機制（點擊攻擊、自動攻擊）
- 寵物協助戰鬥
- 怪物 AI 系統（巡邏、追擊、攻擊）
- 傷害計算與血量管理
- 攻擊範圍檢測

### 4. 武器系統
- 武器裝備與切換
- 武器攻擊特效
- 武器升級系統
- 揮劍動畫與軌跡效果

### 5. 抽卡系統
- 抽卡機制與機率控制
- 金幣扣除系統
- 抽卡動畫與結果展示
- 抽卡歷史記錄

### 6. 區域系統
- 戰鬥區與安全區檢測
- 區域進入/離開事件
- 區域特殊效果

## 技術架構需求

### 1. Knit 架構層
- **服務端服務**：PlayerService, PetService, WeaponService, CombatService, GachaService, ZoneService
- **客戶端控制器**：PetController, CombatController, GachaController, UIController

### 2. Matter ECS 系統層
- **實體**：PlayerEntity, PetEntity, MonsterEntity
- **組件**：Position, Health, Damage, Pet, FollowTarget, SwordSwing, Target
- **系統**：SwordAttack, PetFollow, PetAttack, Health, MonsterAI, Lifetime

### 3. 資料管理
- ProfileService 整合
- 玩家資料結構設計
- 資料同步機制

### 4. UI 系統
- Fusion 響應式 UI
- Flipper 動畫系統
- UI 組件模組化

## 性能需求
- 目標 FPS：60fps
- 最大同時怪物數：50隻
- 最大同時玩家數：20人
- 網絡延遲容忍：<100ms

## 開發標準
- 使用 TypeScript 類型定義
- 遵循 Roblox 性能最佳實踐
- 實施錯誤處理和日誌記錄
- 單元測試覆蓋率 >80%

## 相容性需求
- 支援 Roblox Studio 最新版本
- 相容於 PC、Mobile、Console 平台
- 支援多語言本地化（繁體中文優先）

創建日期：2025-07-31
