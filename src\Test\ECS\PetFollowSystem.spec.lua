--[[
	PetFollowSystem.spec.lua - PetFollowSystem 單元測試
]]

return function()
	local PetFollowSystem = require(game:GetService("ReplicatedStorage").ECS.Systems.PetFollowSystem)
	local PetEntity = require(game:GetService("ReplicatedStorage").ECS.Entities.PetEntity)
	local PlayerEntity = require(game:GetService("ReplicatedStorage").ECS.Entities.PlayerEntity)
	local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
	
	-- 模擬數據
	local function createMockPlayer()
		return {
			Name = "TestPlayer",
			UserId = 12345,
			DisplayName = "Test Player",
			Character = {
				HumanoidRootPart = {
					Position = Vector3.new(0, 10, 0),
					CFrame = CFrame.new(0, 10, 0),
				}
			},
		}
	end
	
	local function createMockPetData()
		return {
			level = 2,
			experience = 50,
			rarity = "Common",
			obtainedTime = os.time(),
		}
	end
	
	describe("PetFollowSystem", function()
		local mockWorld
		local mockPlayer
		local playerEntityId
		local petEntityId
		
		beforeEach(function()
			-- 創建模擬環境
			mockWorld = Matter.World.new()
			mockPlayer = createMockPlayer()
			
			-- 創建玩家實體
			playerEntityId = PlayerEntity.create(mockWorld, mockPlayer, {
				level = 5,
				stats = {
					maxHealth = 150,
					currentHealth = 150,
					attack = 30,
					defense = 10,
				}
			})
			
			-- 創建寵物實體
			petEntityId = PetEntity.create(
				mockWorld,
				"Slime",
				mockPlayer.UserId,
				playerEntityId,
				createMockPetData(),
				Vector3.new(10, 10, 10) -- 距離主人較遠的位置
			)
		end)
		
		afterEach(function()
			-- 清理
			mockWorld = nil
			mockPlayer = nil
			playerEntityId = nil
			petEntityId = nil
		end)
		
		describe("跟隨邏輯", function()
			it("應該讓寵物跟隨主人", function()
				-- 獲取初始位置
				local petData = PetEntity.getData(mockWorld, petEntityId)
				local playerData = PlayerEntity.getData(mockWorld, playerEntityId)
				
				local initialPetPos = petData.position.position
				local playerPos = playerData.position.position
				
				-- 確保寵物距離主人較遠
				local initialDistance = (initialPetPos - playerPos).Magnitude
				expect(initialDistance).to.be.greaterThan(5)
				
				-- 運行跟隨系統
				PetFollowSystem(mockWorld, 1.0) -- 1秒的 deltaTime
				
				-- 檢查寵物是否向主人移動
				local updatedPetData = PetEntity.getData(mockWorld, petEntityId)
				local newPetPos = updatedPetData.position.position
				
				local newDistance = (newPetPos - playerPos).Magnitude
				expect(newDistance).to.be.lessThan(initialDistance)
			end)
			
			it("應該在適當距離停止跟隨", function()
				-- 將寵物放在主人附近
				local playerData = PlayerEntity.getData(mockWorld, playerEntityId)
				local nearPosition = playerData.position.position + Vector3.new(3, 0, 0)
				
				PetEntity.updatePosition(mockWorld, petEntityId, nearPosition, CFrame.new(nearPosition))
				
				-- 運行跟隨系統
				PetFollowSystem(mockWorld, 1.0)
				
				-- 檢查寵物是否保持在適當距離
				local petData = PetEntity.getData(mockWorld, petEntityId)
				local distance = (petData.position.position - playerData.position.position).Magnitude
				
				-- 寵物應該在跟隨距離內
				expect(distance).to.be.lessThan(8) -- 跟隨距離通常是5-8
			end)
			
			it("應該處理主人移動", function()
				-- 移動主人到新位置
				local newPlayerPos = Vector3.new(20, 10, 20)
				PlayerEntity.updatePosition(mockWorld, playerEntityId, newPlayerPos, CFrame.new(newPlayerPos))
				
				-- 運行跟隨系統多次
				for i = 1, 5 do
					PetFollowSystem(mockWorld, 1.0)
				end
				
				-- 檢查寵物是否跟上主人
				local petData = PetEntity.getData(mockWorld, petEntityId)
				local playerData = PlayerEntity.getData(mockWorld, playerEntityId)
				
				local distance = (petData.position.position - playerData.position.position).Magnitude
				expect(distance).to.be.lessThan(10) -- 應該跟上主人
			end)
		end)
		
		describe("傳送邏輯", function()
			it("應該在距離過遠時傳送寵物", function()
				-- 將寵物放在很遠的地方
				local farPosition = Vector3.new(100, 10, 100)
				PetEntity.updatePosition(mockWorld, petEntityId, farPosition, CFrame.new(farPosition))
				
				-- 運行跟隨系統
				PetFollowSystem(mockWorld, 1.0)
				
				-- 檢查寵物是否被傳送到主人附近
				local petData = PetEntity.getData(mockWorld, petEntityId)
				local playerData = PlayerEntity.getData(mockWorld, playerEntityId)
				
				local distance = (petData.position.position - playerData.position.position).Magnitude
				expect(distance).to.be.lessThan(10) -- 應該被傳送到附近
			end)
			
			it("應該在主人改變高度時調整寵物位置", function()
				-- 將主人移動到高處
				local highPosition = Vector3.new(0, 50, 0)
				PlayerEntity.updatePosition(mockWorld, playerEntityId, highPosition, CFrame.new(highPosition))
				
				-- 運行跟隨系統
				PetFollowSystem(mockWorld, 1.0)
				
				-- 檢查寵物是否調整了高度
				local petData = PetEntity.getData(mockWorld, petEntityId)
				expect(petData.position.position.Y).to.be.greaterThan(30) -- 應該跟隨主人到高處
			end)
		end)
		
		describe("狀態管理", function()
			it("應該只處理活躍的寵物", function()
				-- 設置寵物為非活躍
				PetEntity.setActive(mockWorld, petEntityId, false)
				
				-- 獲取初始位置
				local initialPetData = PetEntity.getData(mockWorld, petEntityId)
				local initialPos = initialPetData.position.position
				
				-- 運行跟隨系統
				PetFollowSystem(mockWorld, 1.0)
				
				-- 檢查寵物是否沒有移動
				local updatedPetData = PetEntity.getData(mockWorld, petEntityId)
				local newPos = updatedPetData.position.position
				
				expect(newPos).to.equal(initialPos) -- 位置應該沒有改變
			end)
			
			it("應該處理無效的跟隨目標", function()
				-- 設置無效的跟隨目標
				PetEntity.setFollowTarget(mockWorld, petEntityId, 99999, 5)
				
				-- 運行跟隨系統不應該出錯
				expect(function()
					PetFollowSystem(mockWorld, 1.0)
				end).never.to.throw()
			end)
		end)
		
		describe("性能測試", function()
			it("應該能夠處理多個寵物", function()
				-- 創建多個寵物
				local petIds = {}
				for i = 1, 10 do
					local petId = PetEntity.create(
						mockWorld,
						"Slime",
						mockPlayer.UserId,
						playerEntityId,
						createMockPetData(),
						Vector3.new(i * 5, 10, i * 5)
					)
					table.insert(petIds, petId)
				end
				
				-- 測量運行時間
				local startTime = tick()
				PetFollowSystem(mockWorld, 1.0)
				local endTime = tick()
				
				local executionTime = endTime - startTime
				expect(executionTime).to.be.lessThan(0.1) -- 應該在100ms內完成
				
				-- 檢查所有寵物都有正確的行為
				for _, petId in ipairs(petIds) do
					local petData = PetEntity.getData(mockWorld, petId)
					expect(petData).to.be.ok()
				end
			end)
		end)
		
		describe("邊界情況", function()
			it("應該處理主人實體不存在的情況", function()
				-- 移除主人實體
				mockWorld:despawn(playerEntityId)
				
				-- 運行跟隨系統不應該出錯
				expect(function()
					PetFollowSystem(mockWorld, 1.0)
				end).never.to.throw()
			end)
			
			it("應該處理零 deltaTime", function()
				expect(function()
					PetFollowSystem(mockWorld, 0)
				end).never.to.throw()
			end)
			
			it("應該處理負 deltaTime", function()
				expect(function()
					PetFollowSystem(mockWorld, -1.0)
				end).never.to.throw()
			end)
		end)
	end)
end
