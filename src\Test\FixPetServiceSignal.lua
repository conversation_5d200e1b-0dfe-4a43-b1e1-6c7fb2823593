--[[
	FixPetServiceSignal.lua - 修復 PetService 信號問題
]]

print("🔧 PetService 信號修復指南")
print("=" .. string.rep("=", 50))

print("❗ 問題：PetService:_sendPetDex 使用了錯誤的信號")
print("❗ 當前：self.Client.GetPetDex:Fire")
print("❗ 應該：self.Client.PetDataUpdated:Fire")

print([[

修復代碼：

-- 發送寵物圖鑑數據
function PetService:_sendPetDex(player)
	local profile = self.PlayerService:GetPlayerProfile(player)
	if profile then
		print("🐾 Sending pet data to:", player.Name)
		print("  Owned pets:", profile.Data.ownedPets)
		self.Client.PetDataUpdated:Fire(player, profile.Data.ownedPets, profile.Data.petDex)
	else
		warn("❌ No profile found for player:", player.Name)
	end
end

]])

print("🔧 請手動應用這個修復到 PetService.lua 的 _sendPetDex 函數")
print("📍 位置：大約第 239-244 行")

print("\n💡 其他可能的問題：")
print("1. 玩家檔案載入時機問題")
print("2. 客戶端請求時機太早")
print("3. 信號連接問題")

print("\n🧪 測試步驟：")
print("1. 修復 _sendPetDex 函數")
print("2. 運行 QuickPetFix 腳本")
print("3. 檢查控制台輸出")
print("4. 測試召喚按鈕")

return true
