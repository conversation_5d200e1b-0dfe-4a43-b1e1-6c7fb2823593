--[[
	CombatController.lua - 戰鬥控制器
	玩家輸入：攻擊鍵 / 使用技能
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- 本地化
local i18n = require(game:GetService("ReplicatedStorage").Shared.i18n)

local CombatController = Knit.CreateController({
	Name = "CombatController",
})

-- 私有變量
local player = Players.LocalPlayer
local mouse = player:GetMouse()

-- 戰鬥狀態
local isAttacking = false
local lastAttackTime = 0
local attackCooldown = 1.5
local currentTarget = nil
local autoAttackEnabled = false

-- 特效
local attackEffects = {}

function CombatController:KnitStart()
	print("⚔️ CombatController started")
	
	-- 獲取服務
	self.CombatService = Knit.GetService("CombatService")
	self.UIController = Knit.GetController("UIController")
	
	-- 監聽服務端事件
	self:_connectToServices()
	
	-- 設置輸入監聽
	self:_setupInputHandlers()
	
	-- 設置自動攻擊
	self:_setupAutoAttack()
end

function CombatController:KnitInit()
	-- 初始化時不需要其他控制器
end

-- 連接到服務端服務
function CombatController:_connectToServices()
	-- 監聽攻擊結果
	self.CombatService.AttackResult:Connect(function(targetId, damage, wasKilled)
		self:_handleAttackResult(targetId, damage, wasKilled)
	end)
	
	-- 監聽受到傷害
	self.CombatService.TakeDamage:Connect(function(damage, sourceId)
		self:_handleTakeDamage(damage, sourceId)
	end)
	
	-- 監聽戰鬥更新
	self.CombatService.CombatUpdate:Connect(function(currentHealth, maxHealth)
		self:_handleCombatUpdate(currentHealth, maxHealth)
	end)
	
	-- 監聽目標被擊敗
	self.CombatService.TargetDefeated:Connect(function(targetId)
		self:_handleTargetDefeated(targetId)
	end)
end

-- 設置輸入處理
function CombatController:_setupInputHandlers()
	-- 鍵盤輸入
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		if input.KeyCode == Enum.KeyCode.Space then
			-- 空格鍵攻擊
			self:RequestAttack()
		elseif input.KeyCode == Enum.KeyCode.Q then
			-- Q鍵技能1
			self:UseSkill(1)
		elseif input.KeyCode == Enum.KeyCode.E then
			-- E鍵技能2
			self:UseSkill(2)
		elseif input.KeyCode == Enum.KeyCode.R then
			-- R鍵技能3
			self:UseSkill(3)
		elseif input.KeyCode == Enum.KeyCode.T then
			-- T鍵切換自動攻擊
			self:ToggleAutoAttack()
		end
	end)
	
	-- 滑鼠點擊攻擊
	mouse.Button1Down:Connect(function()
		if not UserInputService:IsKeyDown(Enum.KeyCode.LeftControl) then
			self:RequestAttack()
		end
	end)
	
	-- 右鍵選擇目標
	mouse.Button2Down:Connect(function()
		self:SelectTarget()
	end)
end

-- 設置自動攻擊
function CombatController:_setupAutoAttack()
	-- 每0.5秒檢查一次自動攻擊
	task.spawn(function()
		while true do
			task.wait(0.5)
			
			if autoAttackEnabled and not isAttacking then
				local target = self:_findNearestEnemy()
				if target then
					self:RequestAttackTarget(target)
				end
			end
		end
	end)
end

-- 請求攻擊
function CombatController:RequestAttack()
	local currentTime = tick()
	
	-- 檢查攻擊冷卻
	if currentTime - lastAttackTime < attackCooldown then
		return
	end
	
	-- 查找目標
	local target = currentTarget or self:_findNearestEnemy()
	if not target then
		if self.UIController then
			self.UIController:ShowNotification(
				"沒有找到攻擊目標",
				1,
				Color3.fromRGB(255, 150, 0)
			)
		end
		return
	end
	
	self:RequestAttackTarget(target)
end

-- 請求攻擊指定目標
function CombatController:RequestAttackTarget(target)
	local currentTime = tick()
	
	-- 檢查攻擊冷卻
	if currentTime - lastAttackTime < attackCooldown then
		return
	end
	
	-- 檢查距離
	if not self:_isInAttackRange(target) then
		if self.UIController then
			self.UIController:ShowNotification(
				i18n.t("combat.out_of_range"),
				1,
				Color3.fromRGB(255, 150, 0)
			)
		end
		return
	end
	
	-- 發送攻擊請求到服務端
	self.CombatService.AttackTarget:Fire("Monster", target)
	
	-- 更新本地狀態
	isAttacking = true
	lastAttackTime = currentTime
	currentTarget = target
	
	-- 播放攻擊動畫
	self:_playAttackAnimation()
	
	-- 重置攻擊狀態
	task.wait(0.5)
	isAttacking = false
end

-- 使用技能
function CombatController:UseSkill(skillIndex)
	print("🔥 Using skill:", skillIndex)
	
	-- 這裡可以添加技能邏輯
	-- 例如：不同的技能有不同的效果和冷卻時間
	
	if self.UIController then
		self.UIController:ShowNotification(
			"技能 " .. skillIndex .. " 使用中...",
			2,
			Color3.fromRGB(100, 150, 255)
		)
	end
end

-- 切換自動攻擊
function CombatController:ToggleAutoAttack()
	autoAttackEnabled = not autoAttackEnabled
	
	if self.UIController then
		local message = autoAttackEnabled and "自動攻擊：開啟" or "自動攻擊：關閉"
		local color = autoAttackEnabled and Color3.fromRGB(0, 255, 0) or Color3.fromRGB(255, 100, 100)
		
		self.UIController:ShowNotification(message, 2, color)
	end
	
	print("⚔️ Auto attack:", autoAttackEnabled and "enabled" or "disabled")
end

-- 選擇目標
function CombatController:SelectTarget()
	local target = mouse.Target
	if target and target.Parent then
		-- 檢查是否是有效的敵人目標
		local model = target.Parent
		if model:FindFirstChild("Humanoid") and model.Name:find("Monster") then
			currentTarget = model
			
			if self.UIController then
				self.UIController:ShowNotification(
					"已選擇目標：" .. model.Name,
					2,
					Color3.fromRGB(255, 255, 0)
				)
			end
			
			print("🎯 Target selected:", model.Name)
		end
	end
end

-- 查找最近的敵人
function CombatController:_findNearestEnemy()
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return nil
	end
	
	local playerPosition = player.Character.HumanoidRootPart.Position
	local nearestEnemy = nil
	local nearestDistance = math.huge
	local maxSearchRange = 50 -- 最大搜索範圍
	
	-- 搜索 workspace 中的怪物
	for _, obj in pairs(workspace:GetChildren()) do
		if obj:IsA("Model") and obj.Name:find("Monster") and obj:FindFirstChild("Humanoid") then
			local humanoidRootPart = obj:FindFirstChild("HumanoidRootPart")
			if humanoidRootPart then
				local distance = (humanoidRootPart.Position - playerPosition).Magnitude
				
				if distance < maxSearchRange and distance < nearestDistance then
					-- 檢查怪物是否還活著
					local humanoid = obj:FindFirstChild("Humanoid")
					if humanoid and humanoid.Health > 0 then
						nearestEnemy = obj
						nearestDistance = distance
					end
				end
			end
		end
	end
	
	return nearestEnemy
end

-- 檢查是否在攻擊範圍內
function CombatController:_isInAttackRange(target)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return false
	end
	
	if not target or not target:FindFirstChild("HumanoidRootPart") then
		return false
	end
	
	local playerPosition = player.Character.HumanoidRootPart.Position
	local targetPosition = target.HumanoidRootPart.Position
	local distance = (playerPosition - targetPosition).Magnitude
	
	return distance <= 10 -- 攻擊範圍10格
end

-- 播放攻擊動畫
function CombatController:_playAttackAnimation()
	if not player.Character or not player.Character:FindFirstChild("Humanoid") then
		return
	end
	
	local humanoid = player.Character.Humanoid
	
	-- 播放攻擊動畫（如果有的話）
	-- local attackAnim = humanoid:LoadAnimation(attackAnimationId)
	-- attackAnim:Play()
	
	-- 創建劍光特效
	self:_createSwordTrail()
	
	print("⚔️ Attack animation played")
end

-- 創建劍光軌跡
function CombatController:_createSwordTrail()
	if not player.Character then return end
	
	-- 這裡可以創建劍光特效
	-- 例如：使用 Beam 或 Trail 對象
	
	print("✨ Sword trail effect created")
end

-- 處理攻擊結果
function CombatController:_handleAttackResult(targetId, damage, wasKilled)
	print("⚔️ Attack result:", damage, "damage,", wasKilled and "killed" or "hit")
	
	-- 顯示傷害數字
	self:_showDamageNumber(damage, wasKilled)
	
	-- 播放命中特效
	self:_playHitEffect(targetId, wasKilled)
	
	if self.UIController then
		local message = wasKilled and 
			i18n.t("combat.enemy_defeated") or 
			i18n.t("combat.attack_hit", {damage = damage})
		
		local color = wasKilled and Color3.fromRGB(255, 215, 0) or Color3.fromRGB(255, 150, 100)
		
		self.UIController:ShowNotification(message, 2, color)
	end
end

-- 處理受到傷害
function CombatController:_handleTakeDamage(damage, sourceId)
	print("💔 Took damage:", damage, "from", sourceId)
	
	-- 播放受傷特效
	self:_playDamageEffect(damage)
	
	if self.UIController then
		self.UIController:ShowNotification(
			"受到 " .. damage .. " 點傷害",
			1.5,
			Color3.fromRGB(255, 100, 100)
		)
	end
end

-- 處理戰鬥更新
function CombatController:_handleCombatUpdate(currentHealth, maxHealth)
	-- 更新 UI 血量顯示
	if self.UIController then
		self.UIController:UpdateHealth(currentHealth, maxHealth)
	end
	
	-- 檢查低血量警告
	if currentHealth / maxHealth < 0.3 then
		if self.UIController then
			self.UIController:ShowNotification(
				"⚠️ 血量過低！",
				2,
				Color3.fromRGB(255, 50, 50)
			)
		end
	end
end

-- 處理目標被擊敗
function CombatController:_handleTargetDefeated(targetId)
	-- 清除當前目標
	if currentTarget and currentTarget == targetId then
		currentTarget = nil
	end
	
	print("🎉 Target defeated:", targetId)
end

-- 顯示傷害數字
function CombatController:_showDamageNumber(damage, isCritical)
	-- 這裡可以創建浮動的傷害數字特效
	print("💥 Damage number:", damage, isCritical and "(CRITICAL)" or "")
end

-- 播放命中特效
function CombatController:_playHitEffect(targetId, wasKilled)
	-- 這裡可以播放命中特效
	print("💥 Hit effect for target:", targetId)
end

-- 播放受傷特效
function CombatController:_playDamageEffect(damage)
	-- 這裡可以播放受傷特效（例如：螢幕閃紅）
	print("💔 Damage effect:", damage)
end

-- 獲取當前目標
function CombatController:GetCurrentTarget()
	return currentTarget
end

-- 設置目標
function CombatController:SetTarget(target)
	currentTarget = target
end

-- 檢查是否在攻擊中
function CombatController:IsAttacking()
	return isAttacking
end

-- 檢查自動攻擊狀態
function CombatController:IsAutoAttackEnabled()
	return autoAttackEnabled
end

return CombatController
