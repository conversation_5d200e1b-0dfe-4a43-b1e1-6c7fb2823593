--[[
	CompleteGachaTest.lua - 完整的抽卡功能測試
	包括金幣檢查、單抽、十連抽的完整測試
]]

local Players = game:GetService("Players")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🎰 Complete Gacha Test Starting...")
print("=" .. string.rep("=", 50))

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started, waiting for services...")
	
	-- 等待服務初始化
	task.wait(3)
	
	-- 獲取服務
	local GachaService = Knit.GetService("GachaService")
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	if not GachaService then
		warn("❌ GachaService not found!")
		return
	end
	
	print("✅ GachaService found")
	
	-- 檢查玩家檔案
	local profile = PlayerProfile.getProfile(player)
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Player profile found")
	print("  Player:", player.Name)
	print("  Level:", profile.Data.level)
	print("  Current coins:", profile.Data.coins)
	print("  Current gems:", profile.Data.gems)
	
	-- 確保有足夠金幣進行測試
	if profile.Data.coins < 1000 then
		print("💰 Adding coins for testing...")
		local coinsToAdd = 1000 - profile.Data.coins
		PlayerProfile.addCoins(player, coinsToAdd)
		print("  Added", coinsToAdd, "coins")
		print("  New balance:", profile.Data.coins)
	end
	
	-- 測試結果追蹤
	local testResults = {
		dataRequest = false,
		singlePull = false,
		tenPull = false,
		insufficientFunds = false,
	}
	
	-- 監聽抽卡結果
	GachaService.GachaResult:Connect(function(results, poolType, isTenPull)
		if isTenPull then
			testResults.tenPull = true
			print("\n🎉 TEN PULL SUCCESS!")
			print("  Pool:", poolType)
			print("  Results count:", #results)
			print("  Results:")
			for i, result in ipairs(results) do
				print("    " .. i .. ":", result.name, "(" .. result.rarity .. ")")
			end
		else
			testResults.singlePull = true
			print("\n✅ SINGLE PULL SUCCESS!")
			print("  Pool:", poolType)
			print("  Result:", results[1].name, "(" .. results[1].rarity .. ")")
		end
	end)
	
	-- 監聽數據更新
	GachaService.GachaDataUpdated:Connect(function(data)
		testResults.dataRequest = true
		print("\n✅ GACHA DATA RECEIVED!")
		print("  History count:", #(data.history or {}))
		print("  Single pull cost:", data.costs.singlePullCost)
		print("  Ten pull cost:", data.costs.tenPullCost)
	end)
	
	-- 監聽金幣不足
	GachaService.InsufficientFunds:Connect(function(required, current)
		testResults.insufficientFunds = true
		print("\n💰 INSUFFICIENT FUNDS (Expected for testing)")
		print("  Required:", required)
		print("  Current:", current)
	end)
	
	-- 開始測試序列
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 1: Request Gacha Data")
	print(string.rep("-", 30))
	GachaService.GetGachaData:Fire()
	
	task.wait(3)
	
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 2: Single Pull")
	print(string.rep("-", 30))
	print("Current coins before single pull:", profile.Data.coins)
	GachaService.SinglePull:Fire("pet")
	
	task.wait(3)
	
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 3: Ten Pull")
	print(string.rep("-", 30))
	print("Current coins before ten pull:", profile.Data.coins)
	GachaService.TenPull:Fire("pet")
	
	task.wait(5)
	
	-- 測試金幣不足情況
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 4: Insufficient Funds Test")
	print(string.rep("-", 30))
	
	-- 減少玩家金幣到很少
	local currentCoins = profile.Data.coins
	if currentCoins > 10 then
		local coinsToRemove = currentCoins - 10
		profile.Data.coins = 10
		print("Reduced coins to 10 for testing")
		
		-- 嘗試十連抽（應該失敗）
		GachaService.TenPull:Fire("pet")
		
		task.wait(2)
		
		-- 恢復金幣
		profile.Data.coins = currentCoins
		print("Restored coins to", currentCoins)
	end
	
	-- 最終結果
	task.wait(2)
	
	print("\n" .. string.rep("=", 50))
	print("🧪 FINAL TEST RESULTS")
	print(string.rep("=", 50))
	
	print("Data Request:", testResults.dataRequest and "✅ PASS" or "❌ FAIL")
	print("Single Pull:", testResults.singlePull and "✅ PASS" or "❌ FAIL")
	print("Ten Pull:", testResults.tenPull and "✅ PASS" or "❌ FAIL")
	print("Insufficient Funds:", testResults.insufficientFunds and "✅ PASS" or "❌ FAIL")
	
	local passCount = 0
	for _, passed in pairs(testResults) do
		if passed then passCount = passCount + 1 end
	end
	
	print("\nOverall Result:", passCount .. "/4 tests passed")
	
	if passCount == 4 then
		print("🎉 ALL TESTS PASSED! Gacha system is fully functional!")
	elseif passCount >= 2 then
		print("⚠️ Most tests passed, but some issues remain.")
	else
		print("❌ Major issues detected. Please check the implementation.")
	end
	
	print("\nFinal player state:")
	print("  Coins:", profile.Data.coins)
	print("  Gacha history:", #profile.Data.gachaHistory, "entries")
	
	print(string.rep("=", 50))
	
end):catch(function(err)
	warn("❌ Test failed with error:", err)
end)

return true
