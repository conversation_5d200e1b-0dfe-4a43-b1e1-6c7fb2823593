--[[
	PetSummonTest.lua - 寵物召喚功能測試
	測試寵物召喚、跟隨、收回功能
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🐾 Pet Summon Test Starting...")
print("=" .. string.rep("=", 50))

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

Knit.OnStart():andThen(function()
	print("✅ Knit started, waiting for services...")
	
	-- 等待服務初始化
	task.wait(3)
	
	-- 獲取服務
	local PetService = Knit.GetService("PetService")
	local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	if not PetService then
		warn("❌ PetService not found!")
		return
	end
	
	print("✅ PetService found")
	
	-- 檢查玩家檔案
	local profile = PlayerProfile.getProfile(player)
	if not profile then
		warn("❌ Player profile not found!")
		return
	end
	
	print("✅ Player profile found")
	print("  Player:", player.Name)
	print("  Owned pets:", profile.Data.ownedPets)
	
	-- 確保玩家擁有一些寵物
	if not profile.Data.ownedPets.Slime then
		print("💰 Adding Slime pet for testing...")
		PlayerProfile.addPet(player, "Slime", {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
		print("  Added Slime pet")
	end
	
	-- 測試結果追蹤
	local testResults = {
		petDataReceived = false,
		petSummoned = false,
		petRecalled = false,
		petModelFound = false,
		petFollowing = false,
	}
	
	-- 監聽寵物事件
	PetService.PetDataUpdated:Connect(function(pets, dexData)
		testResults.petDataReceived = true
		print("\n✅ PET DATA RECEIVED!")
		print("  Owned pets count:", pets and #pets or 0)
		print("  Dex entries:", dexData and #dexData or 0)
	end)
	
	PetService.PetSummoned:Connect(function(petId)
		testResults.petSummoned = true
		print("\n🎉 PET SUMMONED!")
		print("  Pet ID:", petId)
		
		-- 檢查寵物模型是否出現
		task.spawn(function()
			local attempts = 0
			while attempts < 10 do
				local petModel = workspace:FindFirstChild("Pet_")
				if petModel then
					testResults.petModelFound = true
					print("✅ Pet model found in workspace:", petModel.Name)
					
					-- 檢查寵物是否跟隨
					local initialPos = petModel.PrimaryPart and petModel.PrimaryPart.Position
					if initialPos then
						task.wait(2)
						local newPos = petModel.PrimaryPart.Position
						local distance = (newPos - initialPos).Magnitude
						if distance > 1 then
							testResults.petFollowing = true
							print("✅ Pet is following (moved", distance, "studs)")
						else
							print("⚠️ Pet is not moving (may not be following)")
						end
					end
					break
				end
				attempts = attempts + 1
				task.wait(0.5)
			end
			
			if not testResults.petModelFound then
				print("❌ Pet model not found in workspace after 5 seconds")
			end
		end)
	end)
	
	PetService.PetRecalled:Connect(function()
		testResults.petRecalled = true
		print("\n✅ PET RECALLED!")
	end)
	
	-- 開始測試序列
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 1: Request Pet Data")
	print(string.rep("-", 30))
	PetService.GetPetDex:Fire()
	
	task.wait(3)
	
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 2: Summon Pet")
	print(string.rep("-", 30))
	print("Attempting to summon Slime...")
	PetService.SummonPet:Fire("Slime")
	
	task.wait(5)
	
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 3: Check Pet Following")
	print(string.rep("-", 30))
	
	-- 移動玩家角色來測試跟隨
	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		local humanoidRootPart = player.Character.HumanoidRootPart
		local originalPos = humanoidRootPart.Position
		
		print("Moving player to test pet following...")
		humanoidRootPart.CFrame = CFrame.new(originalPos + Vector3.new(10, 0, 10))
		
		task.wait(3)
		
		-- 移動回原位
		humanoidRootPart.CFrame = CFrame.new(originalPos)
	end
	
	task.wait(2)
	
	print("\n" .. string.rep("-", 30))
	print("🧪 TEST 4: Recall Pet")
	print(string.rep("-", 30))
	PetService.RecallPet:Fire()
	
	task.wait(3)
	
	-- 最終結果
	print("\n" .. string.rep("=", 50))
	print("🧪 FINAL TEST RESULTS")
	print(string.rep("=", 50))
	
	print("Pet Data Received:", testResults.petDataReceived and "✅ PASS" or "❌ FAIL")
	print("Pet Summoned:", testResults.petSummoned and "✅ PASS" or "❌ FAIL")
	print("Pet Model Found:", testResults.petModelFound and "✅ PASS" or "❌ FAIL")
	print("Pet Following:", testResults.petFollowing and "✅ PASS" or "⚠️ UNKNOWN")
	print("Pet Recalled:", testResults.petRecalled and "✅ PASS" or "❌ FAIL")
	
	local passCount = 0
	for key, passed in pairs(testResults) do
		if passed and key ~= "petFollowing" then -- petFollowing 是可選的
			passCount = passCount + 1
		end
	end
	
	print("\nCore Result:", passCount .. "/4 core tests passed")
	
	if passCount >= 3 then
		print("🎉 Pet summoning system is working!")
		if not testResults.petFollowing then
			print("⚠️ Pet following may need adjustment")
		end
	else
		print("❌ Major issues detected in pet summoning system")
	end
	
	-- 檢查 Matter World 狀態
	print("\nSystem Status:")
	print("  Matter World:", _G.MatterWorld and "✅ Available" or "❌ Not Found")
	
	if _G.MatterWorld then
		local entityCount = 0
		for _ in _G.MatterWorld:query() do
			entityCount = entityCount + 1
		end
		print("  ECS Entities:", entityCount)
	end
	
	print(string.rep("=", 50))
	
end):catch(function(err)
	warn("❌ Test failed with error:", err)
end)

return true
