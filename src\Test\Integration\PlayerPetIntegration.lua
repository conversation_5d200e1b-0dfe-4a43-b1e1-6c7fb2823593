--[[
	PlayerPetIntegration.lua - 玩家與寵物系統集成測試
	測試玩家召喚寵物、寵物跟隨、戰鬥等完整流程
]]

local PlayerEntity = require(game:GetService("ReplicatedStorage").ECS.Entities.PlayerEntity)
local PetEntity = require(game:GetService("ReplicatedStorage").ECS.Entities.PetEntity)
local PetFollowSystem = require(game:GetService("ReplicatedStorage").ECS.Systems.PetFollowSystem)
local PetAttackSystem = require(game:GetService("ReplicatedStorage").ECS.Systems.PetAttackSystem)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)

local function runPlayerPetIntegrationTest()
	print("🔗 Running Player-Pet Integration Test...")
	
	-- 創建測試環境
	local world = Matter.World.new()
	
	-- 模擬玩家
	local mockPlayer = {
		Name = "IntegrationTestPlayer",
		UserId = 99999,
		DisplayName = "Integration Test Player",
		Character = {
			HumanoidRootPart = {
				Position = Vector3.new(0, 10, 0),
				CFrame = CFrame.new(0, 10, 0),
			}
		},
	}
	
	-- 測試步驟1：創建玩家實體
	print("📝 Step 1: Creating player entity...")
	local playerEntityId = PlayerEntity.create(world, mockPlayer, {
		level = 10,
		experience = 500,
		coins = 1000,
		stats = {
			maxHealth = 200,
			currentHealth = 200,
			attack = 50,
			defense = 15,
			criticalChance = 0.2,
			criticalMultiplier = 2.5,
		}
	})
	
	assert(playerEntityId, "❌ Failed to create player entity")
	print("✅ Player entity created:", playerEntityId)
	
	-- 測試步驟2：創建寵物實體
	print("📝 Step 2: Creating pet entity...")
	local petEntityId = PetEntity.create(
		world,
		"FireSpirit",
		mockPlayer.UserId,
		playerEntityId,
		{
			level = 5,
			experience = 120,
			rarity = "Uncommon",
		},
		Vector3.new(20, 10, 20) -- 距離玩家較遠
	)
	
	assert(petEntityId, "❌ Failed to create pet entity")
	print("✅ Pet entity created:", petEntityId)
	
	-- 測試步驟3：驗證初始狀態
	print("📝 Step 3: Verifying initial state...")
	local playerData = PlayerEntity.getData(world, playerEntityId)
	local petData = PetEntity.getData(world, petEntityId)
	
	assert(playerData.player.level == 10, "❌ Player level mismatch")
	assert(petData.pet.petId == "FireSpirit", "❌ Pet ID mismatch")
	assert(petData.pet.ownerId == mockPlayer.UserId, "❌ Pet owner mismatch")
	assert(petData.pet.isActive == true, "❌ Pet should be active")
	
	print("✅ Initial state verified")
	
	-- 測試步驟4：測試寵物跟隨
	print("📝 Step 4: Testing pet following...")
	local initialDistance = (petData.position.position - playerData.position.position).Magnitude
	print("Initial distance:", initialDistance)
	
	-- 運行跟隨系統多次
	for i = 1, 10 do
		PetFollowSystem(world, 1.0)
	end
	
	-- 檢查寵物是否跟隨
	local updatedPetData = PetEntity.getData(world, petEntityId)
	local finalDistance = (updatedPetData.position.position - playerData.position.position).Magnitude
	print("Final distance:", finalDistance)
	
	assert(finalDistance < initialDistance, "❌ Pet should have moved closer to player")
	assert(finalDistance < 10, "❌ Pet should be within following range")
	print("✅ Pet following works correctly")
	
	-- 測試步驟5：移動玩家並測試跟隨
	print("📝 Step 5: Testing player movement and pet following...")
	local newPlayerPos = Vector3.new(50, 10, 50)
	PlayerEntity.updatePosition(world, playerEntityId, newPlayerPos, CFrame.new(newPlayerPos))
	
	-- 運行跟隨系統
	for i = 1, 15 do
		PetFollowSystem(world, 1.0)
	end
	
	-- 檢查寵物是否跟上
	updatedPetData = PetEntity.getData(world, petEntityId)
	local updatedPlayerData = PlayerEntity.getData(world, playerEntityId)
	local distanceAfterMove = (updatedPetData.position.position - updatedPlayerData.position.position).Magnitude
	
	assert(distanceAfterMove < 15, "❌ Pet should follow player to new location")
	print("✅ Pet follows player movement correctly")
	
	-- 測試步驟6：測試寵物升級
	print("📝 Step 6: Testing pet leveling...")
	local initialLevel = updatedPetData.pet.level
	local newLevel = PetEntity.addExperience(world, petEntityId, 200)
	
	if type(newLevel) == "number" and newLevel > initialLevel then
		print("✅ Pet leveled up from", initialLevel, "to", newLevel)
		
		-- 檢查屬性是否更新
		local leveledPetData = PetEntity.getData(world, petEntityId)
		assert(leveledPetData.pet.level == newLevel, "❌ Pet level not updated correctly")
	else
		print("ℹ️ Pet did not level up (expected behavior)")
	end
	
	-- 測試步驟7：測試寵物狀態切換
	print("📝 Step 7: Testing pet state management...")
	
	-- 設置寵物為非活躍
	local success = PetEntity.setActive(world, petEntityId, false)
	assert(success, "❌ Failed to deactivate pet")
	
	local deactivatedPetData = PetEntity.getData(world, petEntityId)
	assert(deactivatedPetData.pet.isActive == false, "❌ Pet should be inactive")
	
	-- 重新激活寵物
	success = PetEntity.setActive(world, petEntityId, true)
	assert(success, "❌ Failed to reactivate pet")
	
	local reactivatedPetData = PetEntity.getData(world, petEntityId)
	assert(reactivatedPetData.pet.isActive == true, "❌ Pet should be active again")
	
	print("✅ Pet state management works correctly")
	
	-- 測試步驟8：測試寵物治療
	print("📝 Step 8: Testing pet healing...")
	
	-- 減少寵物血量
	local petHealthData = PetEntity.getData(world, petEntityId)
	local maxHealth = petHealthData.health.maximum
	world:insert(petEntityId, petHealthData.health:patch({
		current = math.floor(maxHealth * 0.5),
	}))
	
	-- 治療寵物
	local healedAmount = PetEntity.heal(world, petEntityId, 50)
	assert(healedAmount, "❌ Failed to heal pet")
	
	local healedPetData = PetEntity.getData(world, petEntityId)
	assert(healedPetData.health.current > maxHealth * 0.5, "❌ Pet health should have increased")
	
	print("✅ Pet healing works correctly")
	
	-- 測試步驟9：測試玩家升級對寵物的影響
	print("📝 Step 9: Testing player level up...")
	
	local initialPlayerLevel = playerData.player.level
	local playerNewLevel = PlayerEntity.addExperience(world, playerEntityId, 1000)
	
	if type(playerNewLevel) == "number" and playerNewLevel > initialPlayerLevel then
		print("✅ Player leveled up from", initialPlayerLevel, "to", playerNewLevel)
		
		-- 檢查玩家屬性是否更新
		local leveledPlayerData = PlayerEntity.getData(world, playerEntityId)
		assert(leveledPlayerData.player.level == playerNewLevel, "❌ Player level not updated correctly")
		assert(leveledPlayerData.health.maximum > playerData.health.maximum, "❌ Player max health should increase")
	else
		print("ℹ️ Player did not level up (expected behavior)")
	end
	
	-- 測試步驟10：清理測試
	print("📝 Step 10: Cleanup...")
	
	-- 檢查實體是否仍然存在
	assert(PlayerEntity.isAlive(world, playerEntityId), "❌ Player should still be alive")
	assert(PetEntity.isAlive(world, petEntityId), "❌ Pet should still be alive")
	
	-- 移除實體
	world:despawn(playerEntityId)
	world:despawn(petEntityId)
	
	print("✅ Cleanup completed")
	
	print("🎉 Player-Pet Integration Test PASSED!")
	return true
end

-- 運行測試
local success, error = pcall(runPlayerPetIntegrationTest)

if success then
	print("✅ Integration test completed successfully")
else
	warn("❌ Integration test failed:", error)
end

return success
