--[[
	PetFollowSystem.lua - 寵物跟隨系統
	處理寵物跟隨主人的邏輯
]]

local PositionComponent = require(script.Parent.Parent.Components.PositionComponent)
local PetComponent = require(script.Parent.Parent.Components.PetComponent)
local FollowTargetComponent = require(script.Parent.Parent.Components.FollowTargetComponent)

local function PetFollowSystem(world, deltaTime)
	-- 查詢所有需要跟隨的寵物
	for petId, pet, followTarget, position in world:query(PetComponent, FollowTargetComponent, PositionComponent) do
		if followTarget.isFollowing and pet.isActive then
			-- 查找主人的位置
			local ownerPosition = nil
			
			-- 通過 ownerId 查找主人實體
			for ownerId, ownerPos in world:query(PositionComponent) do
				-- 這裡需要一個方法來匹配玩家實體和 userId
				-- 暫時使用 targetId 作為主人實體ID
				if ownerId == followTarget.targetId then
					ownerPosition = ownerPos.position
					break
				end
			end
			
			if ownerPosition then
				local petPosition = position.position
				local distance = (ownerPosition - petPosition).Magnitude
				
				-- 如果距離太遠，傳送到主人附近
				if distance > followTarget.maxDistance then
					local teleportPosition = ownerPosition + Vector3.new(
						math.random(-5, 5),
						2,
						math.random(-5, 5)
					)
					
					world:insert(petId, position:patch({
						position = teleportPosition,
						lastPosition = petPosition,
					}))
					
					print("🐾 Pet teleported to owner")
					
				-- 如果距離超過跟隨距離，開始移動
				elseif distance > followTarget.followDistance then
					-- 計算移動方向
					local direction = (ownerPosition - petPosition).Unit
					local targetPosition = ownerPosition - direction * followTarget.followDistance
					
					-- 計算移動速度
					local moveSpeed = followTarget.speed
					
					-- 如果距離很遠，增加移動速度
					if distance > followTarget.followDistance * 3 then
						moveSpeed = moveSpeed * 1.5
					end
					
					-- 計算新的位置
					local moveDirection = (targetPosition - petPosition).Unit
					local newPosition = petPosition + moveDirection * moveSpeed * deltaTime
					
					-- 確保寵物不會穿過地面
					newPosition = Vector3.new(newPosition.X, math.max(newPosition.Y, ownerPosition.Y), newPosition.Z)
					
					-- 更新位置
					world:insert(petId, position:patch({
						position = newPosition,
						rotation = CFrame.lookAt(newPosition, newPosition + moveDirection),
						lastPosition = petPosition,
						velocity = moveDirection * moveSpeed,
					}))
					
					-- 在 Roblox 中更新實際模型位置
					local petModel = workspace:FindFirstChild("Pet_" .. petId)
					if petModel and petModel.PrimaryPart then
						petModel:SetPrimaryPartCFrame(CFrame.lookAt(newPosition, newPosition + moveDirection))
					end
				else
					-- 距離足夠近，停止移動
					world:insert(petId, position:patch({
						velocity = Vector3.new(),
					}))
				end
				
				-- 更新最後目標位置
				world:insert(petId, followTarget:patch({
					lastTargetPosition = ownerPosition,
				}))
			end
		end
	end
end

return PetFollowSystem
