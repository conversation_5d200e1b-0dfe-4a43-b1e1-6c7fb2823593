--[[
	PetCommands.lua - 寵物召喚命令
	提供直接的寵物召喚命令，繞過 UI 問題
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local player = Players.LocalPlayer or Players.PlayerAdded:Wait()

print("🐾 Pet Commands Loaded!")
print("💡 Available commands:")
print("  F5 - Summon Slime")
print("  F6 - Summon Wolf") 
print("  F7 - Summon FireSpirit")
print("  F8 - Recall Pet")

-- 等待 Knit 啟動
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

local PetService
local PlayerProfile

Knit.OnStart():andThen(function()
	PetService = Knit.GetService("PetService")
	PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
	
	print("✅ Pet commands ready!")
end)

-- 召喚寵物函數
local function summonPet(petId)
	if not PetService then
		warn("❌ PetService not ready")
		return
	end
	
	-- 確保玩家有這隻寵物
	local profile = PlayerProfile.getProfile(player)
	if not profile then
		warn("❌ Player profile not found")
		return
	end
	
	if not profile.Data.ownedPets[petId] then
		print("💰 Adding", petId, "pet...")
		PlayerProfile.addPet(player, petId, {
			level = 1,
			experience = 0,
			rarity = "Common",
			obtainedTime = os.time(),
		})
	end
	
	print("🐾 Summoning", petId, "...")
	PetService.SummonPet:Fire(petId)
end

-- 收回寵物函數
local function recallPet()
	if not PetService then
		warn("❌ PetService not ready")
		return
	end
	
	print("🐾 Recalling pet...")
	PetService.RecallPet:Fire()
end

-- 設置快捷鍵
UserInputService.InputBegan:Connect(function(input, gameProcessed)
	if gameProcessed then return end
	
	if input.KeyCode == Enum.KeyCode.F5 then
		summonPet("Slime")
	elseif input.KeyCode == Enum.KeyCode.F6 then
		summonPet("Wolf")
	elseif input.KeyCode == Enum.KeyCode.F7 then
		summonPet("FireSpirit")
	elseif input.KeyCode == Enum.KeyCode.F8 then
		recallPet()
	end
end)

-- 聊天命令處理
player.Chatted:Connect(function(message)
	local command = message:lower()
	
	if command:match("^/summon") then
		local petId = command:match("^/summon%s+(%w+)")
		if petId then
			-- 首字母大寫
			petId = petId:sub(1,1):upper() .. petId:sub(2):lower()
			summonPet(petId)
		else
			summonPet("Slime") -- 默認召喚史萊姆
		end
	elseif command == "/recall" then
		recallPet()
	elseif command == "/pets" then
		-- 顯示可用寵物
		local profile = PlayerProfile and PlayerProfile.getProfile(player)
		if profile then
			print("🐾 Owned pets:")
			for petId, petData in pairs(profile.Data.ownedPets) do
				print("  -", petId, "(Level", petData.level .. ")")
			end
		end
	elseif command == "/pethelp" then
		print("🐾 Pet Commands:")
		print("  /summon [petId] - Summon a pet")
		print("  /recall - Recall current pet")
		print("  /pets - List owned pets")
		print("  F5-F8 - Quick summon hotkeys")
	end
end)

-- 監聽召喚事件
Knit.OnStart():andThen(function()
	PetService.PetSummoned:Connect(function(petId)
		print("🎉 Pet summoned successfully:", petId)
		
		-- 檢查寵物是否出現
		task.spawn(function()
			task.wait(1)
			local found = false
			for _, child in ipairs(workspace:GetChildren()) do
				if child.Name:match("Pet_") then
					found = true
					print("✅ Pet model found:", child.Name)
					if child.PrimaryPart then
						print("  Position:", child.PrimaryPart.Position)
					end
					break
				end
			end
			
			if not found then
				print("❌ Pet model not found in workspace")
				print("💡 Check server console for errors")
			end
		end)
	end)
	
	PetService.PetRecalled:Connect(function()
		print("✅ Pet recalled successfully")
	end)
end)

print("💡 Type /pethelp for command list")

return true
