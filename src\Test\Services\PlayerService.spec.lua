--[[
	PlayerService.spec.lua - PlayerService 單元測試
]]

return function()
	local PlayerService = require(game:GetService("ServerStorage").Server.Services.PlayerService)
	local PlayerEntity = require(game:GetService("ReplicatedStorage").ECS.Entities.PlayerEntity)
	local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
	
	-- 模擬玩家對象
	local function createMockPlayer()
		return {
			Name = "TestPlayer",
			UserId = 12345,
			DisplayName = "Test Player",
			Character = {
				HumanoidRootPart = {
					Position = Vector3.new(0, 10, 0),
					CFrame = CFrame.new(0, 10, 0),
				}
			},
			Parent = game:GetService("Players"),
		}
	end
	
	-- 模擬檔案數據
	local function createMockProfile()
		return {
			Data = _G.MOCK_PLAYER_DATA or {
				level = 1,
				experience = 0,
				coins = 100,
				gems = 0,
				stats = {
					maxHealth = 100,
					currentHealth = 100,
					attack = 20,
					defense = 5,
					criticalChance = 0.1,
					criticalMultiplier = 2.0,
				},
			}
		}
	end
	
	describe("PlayerService", function()
		local mockWorld
		local mockPlayer
		local mockProfile
		
		beforeEach(function()
			-- 創建模擬環境
			mockWorld = Matter.World.new()
			mockPlayer = createMockPlayer()
			mockProfile = createMockProfile()
			
			-- 設置 PlayerService 的 world
			PlayerService.world = mockWorld
		end)
		
		afterEach(function()
			-- 清理
			mockWorld = nil
			mockPlayer = nil
			mockProfile = nil
		end)
		
		describe("玩家實體管理", function()
			it("應該能夠創建玩家實體", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				
				expect(entityId).to.be.ok()
				expect(type(entityId)).to.equal("number")
				
				-- 檢查實體是否有正確的組件
				local playerData = PlayerEntity.getData(mockWorld, entityId)
				expect(playerData).to.be.ok()
				expect(playerData.player).to.be.ok()
				expect(playerData.health).to.be.ok()
				expect(playerData.damage).to.be.ok()
				expect(playerData.position).to.be.ok()
			end)
			
			it("應該正確設置玩家屬性", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				local playerData = PlayerEntity.getData(mockWorld, entityId)
				
				-- 檢查玩家組件
				expect(playerData.player.userId).to.equal(mockPlayer.UserId)
				expect(playerData.player.displayName).to.equal(mockPlayer.DisplayName)
				expect(playerData.player.level).to.equal(mockProfile.Data.level)
				
				-- 檢查血量組件
				expect(playerData.health.current).to.equal(mockProfile.Data.stats.currentHealth)
				expect(playerData.health.maximum).to.equal(mockProfile.Data.stats.maxHealth)
				
				-- 檢查傷害組件
				expect(playerData.damage.attack).to.equal(mockProfile.Data.stats.attack)
				expect(playerData.damage.defense).to.equal(mockProfile.Data.stats.defense)
			end)
			
			it("應該能夠更新玩家位置", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				
				local newPosition = Vector3.new(10, 20, 30)
				local newCFrame = CFrame.new(newPosition)
				
				local success = PlayerEntity.updatePosition(mockWorld, entityId, newPosition, newCFrame)
				expect(success).to.equal(true)
				
				local playerData = PlayerEntity.getData(mockWorld, entityId)
				expect(playerData.position.position).to.equal(newPosition)
			end)
		end)
		
		describe("玩家數據同步", function()
			it("應該能夠從實體同步數據到檔案", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				
				-- 修改實體數據
				PlayerEntity.heal(mockWorld, entityId, 50)
				
				-- 模擬同步過程
				PlayerService.playerEntities = {[mockPlayer] = entityId}
				PlayerService.playerProfiles = {[mockPlayer] = mockProfile}
				
				PlayerService:_syncEntityToProfile(mockPlayer)
				
				-- 檢查檔案是否更新
				expect(mockProfile.Data.stats.currentHealth).to.be.near(150, 1)
			end)
			
			it("應該能夠處理玩家升級", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				
				-- 添加經驗值觸發升級
				local newLevel = PlayerEntity.addExperience(mockWorld, entityId, 200)
				
				expect(newLevel).to.be.ok()
				expect(type(newLevel)).to.equal("number")
				expect(newLevel).to.be.greaterThan(mockProfile.Data.level)
				
				local playerData = PlayerEntity.getData(mockWorld, entityId)
				expect(playerData.player.level).to.equal(newLevel)
			end)
		end)
		
		describe("錯誤處理", function()
			it("應該處理無效的玩家對象", function()
				local invalidPlayer = {
					Name = "InvalidPlayer",
					-- 缺少必要屬性
				}
				
				local entityId = PlayerService:_createPlayerEntity(invalidPlayer, mockProfile.Data)
				expect(entityId).to.equal(nil)
			end)
			
			it("應該處理缺少角色的玩家", function()
				local playerWithoutCharacter = createMockPlayer()
				playerWithoutCharacter.Character = nil
				
				local entityId = PlayerService:_createPlayerEntity(playerWithoutCharacter, mockProfile.Data)
				expect(entityId).to.equal(nil)
			end)
			
			it("應該處理無效的實體ID", function()
				local success = PlayerEntity.updatePosition(mockWorld, 99999, Vector3.new(), CFrame.new())
				expect(success).to.equal(false)
			end)
		end)
		
		describe("玩家狀態檢查", function()
			it("應該正確檢查玩家是否存活", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				
				-- 玩家應該是存活的
				expect(PlayerEntity.isAlive(mockWorld, entityId)).to.equal(true)
				
				-- 設置玩家死亡
				local playerData = PlayerEntity.getData(mockWorld, entityId)
				mockWorld:insert(entityId, playerData.health:patch({
					current = 0,
					isDead = true,
				}))
				
				-- 玩家應該是死亡的
				expect(PlayerEntity.isAlive(mockWorld, entityId)).to.equal(false)
			end)
			
			it("應該能夠復活玩家", function()
				local entityId = PlayerService:_createPlayerEntity(mockPlayer, mockProfile.Data)
				
				-- 設置玩家死亡
				local playerData = PlayerEntity.getData(mockWorld, entityId)
				mockWorld:insert(entityId, playerData.health:patch({
					current = 0,
					isDead = true,
				}))
				
				-- 復活玩家
				local success = PlayerEntity.revive(mockWorld, entityId, 0.5)
				expect(success).to.equal(true)
				
				-- 檢查復活狀態
				expect(PlayerEntity.isAlive(mockWorld, entityId)).to.equal(true)
				
				local updatedData = PlayerEntity.getData(mockWorld, entityId)
				expect(updatedData.health.current).to.be.greaterThan(0)
				expect(updatedData.health.isDead).to.equal(false)
			end)
		end)
	end)
end
